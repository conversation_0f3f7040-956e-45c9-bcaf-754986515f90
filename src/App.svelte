<script lang="ts">
  import { Toaster } from "svelte-sonner";
  import Router, {location} from "svelte-spa-router";
  import { routes } from "./routes";
  import { onMount } from "svelte";
  import {
    getChargingStations,
    getDrivers,
    getFleet,
  } from "./lib/home/<USER>/fleet.service";
  import { fleetStore } from "./lib/store/vehicle.svelte";
  import { chargingStationStore } from "./lib/store/chargingStation.svelte";
  import { driversStore } from "./lib/store/driver.store.svelte";

  onMount(() => {
    if (!$location.endsWith("success")) {
      getFleet((response) => {
        fleetStore.fleet = response.items;
      });

      getChargingStations((response) => {
        chargingStationStore.chargingStation = response.items;
      });
      getDrivers((response) => {
        driversStore.drivers = response.items;
      });
    }
  });
</script>

<Toaster position="top-center" richColors={true} />
<Router {routes} />
