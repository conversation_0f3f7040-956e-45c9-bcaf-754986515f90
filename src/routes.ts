import Login from "./lib/authentication/Login.svelte";
import Register from "./lib/authentication/Register.svelte";
import BatteryMetrics from "./lib/home/<USER>/BatteryMetrics.svelte";
import CarDetails from "./lib/home/<USER>/CarDetails.svelte";
import Success from "./lib/home/<USER>/components/Success.svelte";
import Dashboard from "./lib/home/<USER>/Dashboard.svelte";
import Drivers from "./lib/home/<USER>/Drivers.svelte";
import Fleet from "./lib/home/<USER>/Fleet.svelte";
import Reservations from "./lib/home/<USER>/Reservations.svelte";
import ReserveBattery from "./lib/home/<USER>/ReserveBattery.svelte";
import Trips from "./lib/home/<USER>/Trips.svelte";
import EntityManagement from "./lib/home/<USER>/EntityManagement.svelte";
import EntityInstances from "./lib/home/<USER>/EntityInstances.svelte";

export const routes = {
    "/": Login,
    "/register": Register,
    "/home": Dashboard,
    "/fleet": Fleet,
    "/drivers": Drivers,
    "/car": CarDetails,
    "/trips": Trips,
    "/reserve-battery/:vehicleId": ReserveBattery,
    "/battery-info": BatteryMetrics,
    "/battery-reservations": Reservations,
    "/entities": EntityManagement,
    "/entities/:entityId/instances": EntityInstances,
    "/success": Success,
}