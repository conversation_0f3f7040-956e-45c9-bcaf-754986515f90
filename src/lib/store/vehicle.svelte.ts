import type { Vehicle } from "../types/vehicle.type";

class FleetStore {
    fleet: Vehicle[] = $state([]);
}

class VehicleStore {
    #vehicle?: Vehicle = $state();

    constructor() {
        this.#vehicle = localStorage.getItem('selected-vehicle') !== null ? JSON.parse(localStorage.getItem('selected-vehicle')!) as Vehicle : undefined;
    }


    public get vehicle(): Vehicle | undefined {
        return this.#vehicle
    }


    public set vehicle(v: Vehicle) {
        this.#vehicle = v;
        localStorage.setItem('selected-vehicle', JSON.stringify(v));
    }


}

export const fleetStore = new FleetStore();
export const vehicleStore = new VehicleStore();
