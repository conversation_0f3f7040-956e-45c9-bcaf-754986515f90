import type {
    EntityDefinition,
    EntityInstance,
    EntityConfiguration
} from "../types/entity.type";

// Store for managing entity definitions
class EntityDefinitionStore {
    definitions: EntityDefinition[] = $state([]);
    configurations: Record<string, EntityConfiguration> = $state({});

    constructor() {
        this.loadFromStorage();
    }

    // Add a new entity definition
    addDefinition(definition: EntityDefinition) {
        this.definitions = [...this.definitions, definition];
        this.saveToStorage();
    }

    // Update an existing entity definition
    updateDefinition(id: string, updates: Partial<EntityDefinition>) {
        this.definitions = this.definitions.map(def =>
            def.id === id ? { ...def, ...updates, updatedAt: new Date().toISOString() } : def
        );
        this.saveToStorage();
    }

    // Delete an entity definition
    deleteDefinition(id: string) {
        this.definitions = this.definitions.filter(def => def.id !== id);
        delete this.configurations[id];
        this.saveToStorage();
    }

    // Get entity definition by ID
    getDefinition(id: string): EntityDefinition | undefined {
        return this.definitions.find(def => def.id === id);
    }

    // Get entity configuration by ID
    getConfiguration(id: string): EntityConfiguration | undefined {
        return this.configurations[id];
    }

    // Set entity configuration
    setConfiguration(id: string, config: EntityConfiguration) {
        this.configurations[id] = config;
        this.saveToStorage();
    }

    // Load from localStorage
    private loadFromStorage() {
        try {
            const stored = localStorage.getItem('entity-definitions');
            if (stored) {
                const data = JSON.parse(stored);
                this.definitions = data.definitions || [];
                this.configurations = data.configurations || {};
            }
        } catch (error) {
            console.error('Failed to load entity definitions from storage:', error);
        }
    }

    // Save to localStorage
    private saveToStorage() {
        try {
            localStorage.setItem('entity-definitions', JSON.stringify({
                definitions: this.definitions,
                configurations: this.configurations
            }));
        } catch (error) {
            console.error('Failed to save entity definitions to storage:', error);
        }
    }
}

// Store for managing entity instances
class EntityInstanceStore {
    instances: Record<string, EntityInstance[]> = $state({}); // entityId -> instances

    constructor() {
        this.loadFromStorage();
    }

    // Add a new entity instance
    addInstance(entityId: string, instance: Omit<EntityInstance, 'id' | 'createdAt' | 'updatedAt'>) {
        const newInstance: EntityInstance = {
            ...instance,
            id: this.generateId(),
            entityId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (!this.instances[entityId]) {
            this.instances[entityId] = [];
        }

        this.instances[entityId] = [...this.instances[entityId], newInstance];
        this.saveToStorage();
        return newInstance;
    }

    // Update an entity instance
    updateInstance(entityId: string, instanceId: string, updates: Partial<EntityInstance>) {
        if (!this.instances[entityId]) return;

        this.instances[entityId] = this.instances[entityId].map(instance =>
            instance.id === instanceId
                ? { ...instance, ...updates, updatedAt: new Date().toISOString() }
                : instance
        );
        this.saveToStorage();
    }

    // Delete an entity instance (soft delete if supported)
    deleteInstance(entityId: string, instanceId: string, softDelete = false) {
        if (!this.instances[entityId]) return;

        if (softDelete) {
            this.instances[entityId] = this.instances[entityId].map(instance =>
                instance.id === instanceId
                    ? { ...instance, deletedAt: new Date().toISOString() }
                    : instance
            );
        } else {
            this.instances[entityId] = this.instances[entityId].filter(
                instance => instance.id !== instanceId
            );
        }
        this.saveToStorage();
    }

    // Get instances for an entity
    getInstances(entityId: string, includeDeleted = false): EntityInstance[] {
        const instances = this.instances[entityId] || [];
        return includeDeleted
            ? instances
            : instances.filter(instance => !instance.deletedAt);
    }

    // Get a specific instance
    getInstance(entityId: string, instanceId: string): EntityInstance | undefined {
        const instances = this.instances[entityId] || [];
        return instances.find(instance => instance.id === instanceId);
    }

    // Search instances
    searchInstances(entityId: string, query: string, searchFields: string[]): EntityInstance[] {
        const instances = this.getInstances(entityId);
        if (!query.trim()) return instances;

        const lowerQuery = query.toLowerCase();
        return instances.filter(instance => {
            return searchFields.some(field => {
                const value = instance.data[field];
                if (value == null) return false;
                return String(value).toLowerCase().includes(lowerQuery);
            });
        });
    }

    // Generate unique ID
    private generateId(): string {
        return `entity_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    // Load from localStorage
    private loadFromStorage() {
        try {
            const stored = localStorage.getItem('entity-instances');
            if (stored) {
                this.instances = JSON.parse(stored);
            }
        } catch (error) {
            console.error('Failed to load entity instances from storage:', error);
        }
    }

    // Save to localStorage
    private saveToStorage() {
        try {
            localStorage.setItem('entity-instances', JSON.stringify(this.instances));
        } catch (error) {
            console.error('Failed to save entity instances to storage:', error);
        }
    }
}

// Export store instances
export const entityDefinitionStore = new EntityDefinitionStore();
export const entityInstanceStore = new EntityInstanceStore();