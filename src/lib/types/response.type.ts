export type ErrorResponse = {
    error: string,
    message: string,
    path: string,
    status: number,
    time_stamp: string
}

export type ApiResponse<T> = {
    items: T[],
    meta_data: MetaData
}

export type MetaData = {
    first: string,
    laset: string,
    total: number,
    current_page: number,
    has_more: boolean
}

export type Response = {
    message: string,
    status: boolean
}