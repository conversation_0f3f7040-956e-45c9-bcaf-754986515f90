// Core field types supported by the dynamic entity system
export type FieldType =
    | 'text'
    | 'textarea'
    | 'number'
    | 'email'
    | 'password'
    | 'date'
    | 'datetime'
    | 'time'
    | 'boolean'
    | 'select'
    | 'multiselect'
    | 'radio'
    | 'checkbox'
    | 'file'
    | 'url'
    | 'phone'
    | 'color'
    | 'range'
    | 'location'
    | 'relationship'
    | 'calculated';

// Validation rule types
export type ValidationRuleType =
    | 'required'
    | 'minLength'
    | 'maxLength'
    | 'min'
    | 'max'
    | 'pattern'
    | 'email'
    | 'url'
    | 'custom';

// Individual validation rule
export interface ValidationRule {
    type: ValidationRuleType;
    value?: any;
    message: string;
}

// Field configuration for entity attributes
export interface EntityField {
    id: string;
    name: string;
    label: string;
    type: FieldType;
    required: boolean;
    defaultValue?: any;
    placeholder?: string;
    helpText?: string;
    validation: ValidationRule[];
    options?: FieldOption[]; // For select, radio, checkbox fields
    multiple?: boolean; // For select and file fields
    min?: number; // For number, range, date fields
    max?: number; // For number, range, date fields
    step?: number; // For number and range fields
    accept?: string; // For file fields
    rows?: number; // For textarea fields
    cols?: number; // For textarea fields
    conditional?: ConditionalLogic; // For conditional field display

    // Relationship properties (for relationship fields)
    relationshipType?: 'one-to-one' | 'one-to-many' | 'many-to-one' | 'many-to-many';
    relatedEntity?: string; // Entity ID or name
    relatedField?: string; // Field name in related entity
    cascadeDelete?: boolean;

    // Calculated field properties
    calculationFormula?: string; // JavaScript expression
    calculationDependencies?: string[]; // Field names this calculation depends on
    recalculateOnChange?: boolean;

    order: number; // Field display order
}

// Options for select, radio, checkbox fields
export interface FieldOption {
    value: string | number | boolean;
    label: string;
    disabled?: boolean;
}

// Conditional logic for field visibility
export interface ConditionalLogic {
    field: string; // Field ID to watch
    operator: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'greaterThan' | 'lessThan' | 'isEmpty' | 'isNotEmpty';
    value: any;
}

// Entity definition
export interface EntityDefinition {
    id: string;
    name: string;
    displayName: string;
    description?: string;
    icon?: string;
    color?: string;
    fields: EntityField[];
    permissions?: EntityPermissions;
    timestamps: boolean; // Whether to include createdAt/updatedAt
    softDelete: boolean; // Whether to support soft delete
    searchableFields: string[]; // Field IDs that are searchable
    displayField: string; // Primary field to display in lists
    createdAt: string;
    updatedAt: string;
    createdBy: string;
}

// Entity permissions
export interface EntityPermissions {
    create: string[]; // User roles that can create
    read: string[]; // User roles that can read
    update: string[]; // User roles that can update
    delete: string[]; // User roles that can delete
}

// Entity instance data
export interface EntityInstance {
    id: string;
    entityId: string;
    data: Record<string, any>;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: string;
    updatedBy?: string;
    deletedAt?: string; // For soft delete
}

// Form layout configuration
export interface FormLayout {
    type: 'single-column' | 'two-column' | 'three-column' | 'tabs' | 'accordion' | 'wizard';
    sections?: FormSection[];
}

// Form section for grouping fields
export interface FormSection {
    id: string;
    title: string;
    description?: string;
    fields: string[]; // Field IDs in this section
    collapsible?: boolean;
    collapsed?: boolean;
}

// Complete entity configuration
export interface EntityConfiguration {
    definition: EntityDefinition;
    formLayout: FormLayout;
    listView: ListViewConfiguration;
    detailView: DetailViewConfiguration;
}

// List view configuration
export interface ListViewConfiguration {
    columns: ListColumn[];
    defaultSort: {
        field: string;
        direction: 'asc' | 'desc';
    };
    pageSize: number;
    searchEnabled: boolean;
    filterEnabled: boolean;
    exportEnabled: boolean;
    bulkActions: BulkAction[];
}

// List column configuration
export interface ListColumn {
    field: string;
    label: string;
    sortable: boolean;
    filterable: boolean;
    width?: string;
    align?: 'left' | 'center' | 'right';
    format?: 'date' | 'currency' | 'number' | 'boolean' | 'custom';
    customRenderer?: string; // Component name for custom rendering
}

// Bulk action configuration
export interface BulkAction {
    id: string;
    label: string;
    icon?: string;
    confirmMessage?: string;
    requiresPermission?: string;
}

// Detail view configuration
export interface DetailViewConfiguration {
    layout: 'single-column' | 'two-column' | 'tabs';
    sections: DetailSection[];
    actions: DetailAction[];
}

// Detail section configuration
export interface DetailSection {
    id: string;
    title: string;
    fields: string[];
    collapsible?: boolean;
    collapsed?: boolean;
}

// Detail action configuration
export interface DetailAction {
    id: string;
    label: string;
    icon?: string;
    type: 'primary' | 'secondary' | 'danger';
    confirmMessage?: string;
    requiresPermission?: string;
}