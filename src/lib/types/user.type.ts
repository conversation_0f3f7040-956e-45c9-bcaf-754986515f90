export type User = {
    id: string;
    name: string;
    email: string;
    company: string;
    phoneNumber: string;
    profilePhoto: string;
    customerSince: string;
    village: string;
    idNumber: string;
    licenseNumber: string;
    address: string;
}

export type Token = {
    accessToken: string,
    expiresIn: number,
    type: string
}

export type AuthResponse = {
    user: User,
    token: Token
}