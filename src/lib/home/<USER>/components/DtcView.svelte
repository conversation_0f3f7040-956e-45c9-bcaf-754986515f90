<script lang="ts">
    import { camelCaseToTitleCase } from "../../../shared/utils/miscellaneous.util";
    import type { Diagnostic } from "../../../types/diagnostic.type";

    let { dtcs }: { dtcs: Diagnostic[] } = $props();
    console.log(dtcs)
</script>

<div class="w-full min-w-fit h-full max-h-[80vh] overflow-hidden">
    <div class="w-full flex mb-10">
        <span class="w-1/3 text-white font-medium">Timestamp</span>
        <span class="w-1/4 text-white font-medium">Source</span>
        <span class="w-7/12 text-white font-medium">Diagnostic Message</span>
    </div>
    <div class="w-full h-full flex flex-col max-h-[70vh] overflow-hidden">
        <div class="w-full h-full overflow-y-auto">
            
            {#each dtcs as dtc}
                <div
                    class="w-full flex items-center mb-5 border border-white rounded-md px-2 py-1"
                >
                    <span class="w-1/3 text-white">{dtc.timeStamp}</span>
                    <span class="w-1/4 text-white"
                        >{camelCaseToTitleCase(dtc.source)}</span
                    >
                    <div class="w-7/12 text-white">
                        {#each dtc.spn as spn, index}
                        <div class="w-full h-full flex"></div>
                            <span class="text-sm font-medium"
                                >{`${camelCaseToTitleCase(spn)}: `}</span
                            >
                            <span class="text-sm">{camelCaseToTitleCase(dtc.fmi[index])}</span>
                        {/each}
                    </div>
                </div>
            {/each}
        </div>
    </div>
</div>
