<script lang="ts">
    import { z } from "zod";
    import Input from "../../../shared/components/Input.svelte";
    import Loader from "../../../shared/components/Loader.svelte";
    import type { NewDriverRequest } from "../../../types/request.type";
    import type { ValidationResult } from "../../../types/validation.type";
    import { validateSchema } from "../../../shared/utils/validation.util";
    import { addDriver } from "../../service/driver.service";
    import { driversStore } from "../../../store/driver.store.svelte";
    import { showSuccessToast } from "../../../shared/utils/toast.util";

    let { onComplete }: { onComplete: () => unknown } = $props();
    let driverRequest: Partial<NewDriverRequest> = $state({});
    let requesting = $state(false);
    let validationResult = $state<ValidationResult>();
    const driverSchema = z.object({
        name: z.string({ required_error: "Must provide driver name" }).trim(),
        email: z
            .string({ required_error: "Must provide driver email" })
            .trim()
            .email({ message: "Must provide a valid email address" }),
        phoneNumber: z
            .string({ required_error: "Must provide driver phone" })
            .trim(),
        idNumber: z
            .string({ required_error: "Must provide driver id number" })
            .trim(),
        licenseNumber: z
            .string({ required_error: "Must provide driver license" })
            .trim(),
        address: z
            .string({ required_error: "Must provide driver address" })
            .trim(),
    });

    const addNewDriver = (event: Event) => {
        event.preventDefault();
        event.stopPropagation();
        requesting = true;
        validationResult = validateSchema(driverSchema, driverRequest);
        if (validationResult.success) {
            addDriver(
                driverRequest,
                (newDriver) => {
                    driversStore.drivers.push(newDriver);
                    showSuccessToast(
                        `${newDriver.name} added to your drivers list`,
                    );
                },
                onComplete,
            );
        } else {
            requesting = false;
        }
    };
</script>

<form class="w-full h-full flex flex-col gap-5" onsubmit={addNewDriver}>
    <div class="flex w-full justify-center">
        <span class="mb-1 font-bold text-white">Add New Driver</span>
    </div>
    <div class="w-full h-full flex flex-col gap-4">
        <div class="flex items-baseline gap-1">
            <label for="name" class="text-left text-white w-1/3"
                >Driver name</label
            >
            <Input
                bind:value={driverRequest.name}
                placeholder="Enter driver name"
                errorMessage={validationResult?.errors["name"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="email" class="text-left text-white w-1/3">Email</label>
            <Input
                bind:value={driverRequest.email}
                placeholder="Enter email"
                errorMessage={validationResult?.errors["email"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="phone_number" class="text-left text-white w-1/3"
                >Phone number</label
            >
            <Input
                bind:value={driverRequest.phoneNumber}
                placeholder="Enter phone number"
                errorMessage={validationResult?.errors["phoneNumber"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="license_number" class="text-left text-white w-1/3"
                >License number</label
            >
            <Input
                bind:value={driverRequest.licenseNumber}
                placeholder="Enter license number"
                errorMessage={validationResult?.errors["licenseNumber"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="id_number" class="text-left text-white w-1/3"
                >ID Number</label
            >
            <Input
                bind:value={driverRequest.idNumber}
                placeholder="Enter ID number"
                errorMessage={validationResult?.errors["idNumber"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="address" class="text-left text-white w-1/3"
                >Address</label
            >
            <Input
                bind:value={driverRequest.address}
                placeholder="Enter address"
                errorMessage={validationResult?.errors["address"]}
            />
        </div>

        <div class="flex justify-end">
            <button
                class="flex justify-center min-w-fit w-24 rounded-md bg-primary text-white py-2 px-4 cursor-pointer overflow-hidden hover:shadow-md"
            >
                {#if requesting}
                    <Loader />
                {:else}
                    Save
                {/if}
            </button>
        </div>
    </div>
</form>
