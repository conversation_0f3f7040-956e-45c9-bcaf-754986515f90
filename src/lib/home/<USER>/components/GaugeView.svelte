<script lang="ts">
    import Gauge from "svelte-gauge";

    let {
        value,
        min,
        max,
        text,
        img,
    }: { value: number; min: number; max: number; text: string; img: string } =
        $props();
</script>

<div class="w-1/2 h-full flex flex-col justify-center items-center gap-1">
    <img src={img} alt="speed-icon" />
    <div
        class="rounded shadow-md w-[90%] h-full bg-gray-100 px-1 py-1 flex justify-center items-center"
    >
        <div class="w-[80%]">
            <Gauge
                displayValue={text}
                startAngle={75}
                stopAngle={285}
                {value}
                start={min}
                stop={max}
                stroke={5}
                color={"purple"}
                class="custom-gauge w-full"
            />
        </div>
    </div>
</div>
