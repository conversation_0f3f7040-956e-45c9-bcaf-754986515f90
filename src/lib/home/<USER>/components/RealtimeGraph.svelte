<script lang="ts">
    import type {
        DeepPartial,
        ISeriesApi,
        LineData,
        LineSeriesOptions,
        LineStyleOptions,
        SeriesOptionsCommon,
        TimeScaleOptions,
        Time,
        WhitespaceData,
    } from "lightweight-charts";
    import { io } from "socket.io-client";
    import { onMount } from "svelte";
    import { Chart, LineSeries } from "svelte-lightweight-charts";
    import { vehicleStore } from "../../../store/vehicle.svelte";
    import { showErrorToast } from "../../../shared/utils/toast.util";
    import { camelCaseToTitleCase } from "../../../shared/utils/miscellaneous.util";

    let {
        title,
    }: { title: "voltage" | "soc" | "current" | "batteryTemperature" } =
        $props();
    let linechart: ISeriesApi<
        "Line",
        Time,
        LineData<Time> | WhitespaceData<Time>,
        LineSeriesOptions,
        DeepPartial<LineStyleOptions & SeriesOptionsCommon>
    > | null;

    let metrics: { time: Time; value: number }[] = $state([]);
    let isLoading = $state(false);
    const socket = io(import.meta.env.VITE_SOCKET_URL, {
        autoConnect: false,
    });
    const timeScaleOptions: DeepPartial<TimeScaleOptions> = {
        secondsVisible: true,
        timeVisible: true,
    };
    onMount(() => {
        metrics = [];
        isLoading = true;
        socket.connect();

        socket.on("connect-error", () => {
            showErrorToast("Error when conneting to server");
        });
        socket.on("connect", () => {
            socket.emit("register-client", vehicleStore.vehicle?.id);
        });

        socket.on("metrics", (data) => {
            const jsonData = JSON.parse(data) as {
                speed: number;
                soc: number;
                batteryTemperature: number;
                voltage: number;
                current: number;
                motorTemperature: number;
            };
            metrics.push({
                value: jsonData[title],
                time: Date.now() as Time,
            });
        });
    });
</script>

{#if isLoading}
    <div class="w-full h-full flex justify-center items-center">
        <div class="spinner w-12 h-12 text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
{:else}
    <Chart
        width={window.innerWidth}
        height={window.innerHeight / 2 - 50}
        timeScale={timeScaleOptions}
    >
        <LineSeries
            data={metrics}
            reactive={true}
            ref={(ref) => (linechart = ref)}
            title={camelCaseToTitleCase(title)}
        />
    </Chart>
{/if}
