<script lang="ts">
    import { push } from "svelte-spa-router";
    import { userStore } from "../../../store/user.svelte";

    let { onMenuClicked }: { onMenuClicked: () => unknown } = $props();
    let dropdownOpen = $state(false);

    const toggleDropdown = () => {
        dropdownOpen = !dropdownOpen;
    };
</script>

<div class="w-full h-[8vh] flex justify-between items-center px-5 bg-secondary">
    <div>
        <button
            class="p-2 cursor-pointer hover:shadow-lg flex justify-center items-center"
            onclick={onMenuClicked}
        >
            <img src="/images/menu.png" alt="" />
        </button>
    </div>
    <div>
        <a href="/#/home">
            <img src="/images/fleet.png" alt="" />
        </a>
    </div>
        <button onclick={toggleDropdown} class="cursor-pointer">
            <img
                src={userStore.user?.profilePhoto}
                alt="avatar"
                class="relative inline-block h-12 w-12 !rounded-full object-cover object-center"
            />
            {#if dropdownOpen}
                <div
                    class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10"
                >
                    <ul class="py-1">
                        <li>
                            <a href="/#/profile"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >Settings</a
                            >
                        </li>
                        <li>
                            <a
                                href="/#/logout"
                                class="block px-4 py-2 text-sm bg-red-500 text-white hover:bg-red-600"
                                >Logout</a
                            >
                        </li>
                    </ul>
                </div>
            {/if}
        </button>
</div>
