<script lang="ts">
    import { z } from "zod";
    import Input from "../../../shared/components/Input.svelte";
    import type { NewVehicleRequest } from "../../../types/request.type";
    import type { ValidationResult } from "../../../types/validation.type";
    import { validateSchema } from "../../../shared/utils/validation.util";
    import { addVehicle } from "../../service/fleet.service";
    import { fleetStore } from "../../../store/vehicle.svelte";
    import { showSuccessToast } from "../../../shared/utils/toast.util";
    import Loader from "../../../shared/components/Loader.svelte";

    let { onComplete }: { onComplete: () => unknown } = $props();
    let vehicleRequest: Partial<NewVehicleRequest> = $state({});
    let validationResult = $state<ValidationResult>();
    let requesting = $state(false);
    let vehicleSchema = z.object({
        name: z.string({ required_error: "Must provide a car name" }),
        make: z.string({ required_error: "Must provide a car make" }),
        plateNumber: z.string({
            required_error: "Must provide a car plate number",
        }),
        serialNumber: z.string({
            required_error: "Must provide a car serial number",
        }),
        weight: z
            .number({ required_error: "Must provide a car weight" })
            .min(0, { message: "Weight can not be negative" }),
        size: z
            .number({ required_error: "Must provide a car size" })
            .min(0, { message: "Weight can not be negative" }),
        vehicleType: z.string({
            required_error: "Must provide a vehicle type",
        }),
    });

    const addNewVehicle = (event: Event) => {
        event.preventDefault();
        event.stopPropagation();
        requesting = true;
        validationResult = validateSchema(vehicleSchema, vehicleRequest);
        if (validationResult.success) {
            addVehicle(
                vehicleRequest,
                (response) => {
                    fleetStore.fleet = [...fleetStore.fleet, response];
                    showSuccessToast("New vehicle added to your fleet");
                },
                onComplete,
            );
        } else {
            requesting = false;
        }
    };
</script>

<form class="w-full h-full flex flex-col gap-5" onsubmit={addNewVehicle}>
    <div class="w-full flex justify-center">
        <span class="mb-1 font-bold text-white">Add New Vehicle</span>
    </div>
    <div class="flex flex-col gap-4">
        <div class="flex items-baseline gap-1">
            <label for="name" class="text-left text-white w-1/3">Car name</label
            >
            <Input
                bind:value={vehicleRequest.name}
                placeholder="Enter car name"
                errorMessage={validationResult?.errors["name"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="make" class="text-left w-1/3">Car make</label>
            <Input
                bind:value={vehicleRequest.make}
                placeholder="Enter car make"
                errorMessage={validationResult?.errors["make"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="plate_number" class="text-left w-1/3"
                >Plate number</label
            >
            <Input
                bind:value={vehicleRequest.plateNumber}
                placeholder="Enter plate number"
                errorMessage={validationResult?.errors["plateNumber"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="serial_number" class="text-left w-1/3"
                >Serial number</label
            >
            <Input
                bind:value={vehicleRequest.serialNumber}
                placeholder="Enter serial number"
                errorMessage={validationResult?.errors["serialNumber"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="weight" class="text-left w-1/3">Weight</label>
            <Input
                bind:value={vehicleRequest.weight}
                placeholder="Enter weight"
                type="number"
                errorMessage={validationResult?.errors["weight"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="size" class="text-left w-1/3">Size</label>
            <Input
                bind:value={vehicleRequest.size}
                placeholder="Enter size"
                type="number"
                errorMessage={validationResult?.errors["size"]}
            />
        </div>
        <div class="flex items-baseline gap-1 text-white">
            <label for="vehicle_type" class="text-left w-1/3"
                >Vehicle type</label
            >
            <Input
                bind:value={vehicleRequest.vehicleType}
                placeholder="Enter type"
                errorMessage={validationResult?.errors["vehicleType"]}
            />
        </div>

        <div class="flex justify-end">
            <button
                class="flex justify-center min-w-fit w-24 rounded-md bg-primary text-white py-2 px-4 cursor-pointer hover:shadow-md"
            >
                {#if requesting}
                    <Loader />
                {:else}
                    Save
                {/if}
            </button>
        </div>
    </div>
</form>
