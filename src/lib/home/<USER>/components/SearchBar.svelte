<script lang="ts">
    import { fleetStore } from "../../../store/vehicle.svelte";
    import type { Vehicle } from "../../../types/vehicle.type";

    let { moveMapCamera }: { moveMapCamera: (vehicle: Vehicle | null) => void } = $props();
    let selectedVehicle = $state(fleetStore.fleet.at(0) || null);
</script>

<select
    class="w-full h-full bg-transparent placeholder:text-slate-400 text-slate-700 text-sm border border-slate-200 rounded pl-3 pr-8 py-2 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-400 shadow-sm focus:shadow-md appearance-none cursor-pointer"
    bind:value={selectedVehicle}
    onchange={() => {
        moveMapCamera(selectedVehicle);
    }}
>
    <option value={null} disabled>Select a vehicle</option>
    {#each fleetStore.fleet.filter((vehicle) => vehicle.lastSeen !== null) as vehicle}
        <option value={vehicle}>{vehicle.plateNumber}</option>
    {/each}
</select>
<svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    stroke-width="1.2"
    stroke="currentColor"
    class="h-5 w-5 ml-1 absolute top-2.5 right-2.5 text-slate-700"
>
    <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"
    />
</svg>