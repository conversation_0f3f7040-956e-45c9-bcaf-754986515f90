import { get, post, put } from "../../shared/utils/network.util";
import type { Battery } from "../../types/battery.type";
import type { ApiResponse } from "../../types/response.type";
import { showSuccessToast } from "../../shared/utils/toast.util";
import type { BatteryReservation } from "../../types/reservation.type";

export const getBatteries = (onSuccess: (response: ApiResponse<Battery>) => unknown, onComplete?: () => unknown) => {
    get<ApiResponse<Battery>>("/battery/all", (response) => onSuccess(response), onComplete);
}
export const getAvailableBatteries = (onSuccess: (response: ApiResponse<Battery>) => unknown, onComplete?: () => unknown) => {
    get<ApiResponse<Battery>>("/battery/allAvailable", (response) => onSuccess(response), onComplete);
}
export const getReservations = (onSuccess: (response: ApiResponse<BatteryReservation>) => unknown, onComplete?: () => unknown) => {
    get<ApiResponse<BatteryReservation>>("/battery/reservations", (response) => onSuccess(response), onComplete);
}

export const reserveBattery = (batteryId: string, vehicleId: string, onSuccess: () => void,) => {
    put(
        `/battery?batteryId=${batteryId}&vehicleId=${vehicleId}`,
        {},
        () => {
            showSuccessToast("Battery reserved successfully!");
            onSuccess();
        }
    );
};
export const cancelReservation = (batteryId: string, onSuccess: () => void,) => {
    put(
        `/battery/cancel?batteryId=${batteryId}`,
        {},
        () => {
            showSuccessToast("Battery reservation cancelled successfully!");
            onSuccess();
        }
    );
};
export const cancelReservationWithRefund = (
    reservationId: string,
    sessionId: string,
    reason: string = "requested_by_customer",
    onSuccess: () => void,
    onComplete?: () => void
) => {
    post(
        `/payment/${reservationId}/cancel-with-refund`,
        {
            sessionId: sessionId,
            reason: reason
        },
        () => {
            showSuccessToast("Battery reservation cancelled and refund processed successfully!");
            onSuccess();
        },
        onComplete
    );
};