<script lang="ts">
    import Modal from "../../shared/components/Modal.svelte";
    import Table from "../../shared/components/Table.svelte";
    import { driversStore } from "../../store/driver.store.svelte";
    import type { User } from "../../types/user.type";
    import Layout from "./Layout.svelte";
    import TripDialog from "./TripDialog.svelte";
    import AssignTrip from "./AssignTrip.svelte";
    import NewDriverForm from "./components/NewDriverForm.svelte";
    import { deleteCall } from "../../shared/utils/network.util";
    import { showSuccessToast } from "../../shared/utils/toast.util";
    import Loader from "../../shared/components/Loader.svelte";

    let showModal = $state(false);
    let showTripsModal = $state(false);
    let showAssignTripModal = $state(false);
    let showDeleteModal = $state(false);
    let requesting = $state(false);
    let selectedDriverId: string | null = $state(null);

    function closeAssignTripModal() {
        showAssignTripModal = false;
    }

    function openAssignTripModal(driverId: string) {
        selectedDriverId = driverId;
        showAssignTripModal = true;
    }

    function closeModal() {
        showModal = false;
    }

    function openModal() {
        showModal = true;
    }

    function closeTripModal() {
        showTripsModal = false;
    }

    function openTripsModal(driverId: string) {
        selectedDriverId = driverId;
        showTripsModal = true;
    }

    const openDeleteModal = (driverId: string) => {
        selectedDriverId = driverId;
        showDeleteModal = true;
    };

    const deleteDriver = () => {
        requesting = true;
        deleteCall(
            `/admin/drivers/${selectedDriverId!}`,
            (_) => {
                driversStore.drivers = driversStore.drivers.filter(
                    (driver) => driver.id !== selectedDriverId,
                );
                showSuccessToast("Driver deleted");
            },
            () => {
                requesting = false;
                showDeleteModal = false;
            },
        );
    };
</script>

<Layout>
    <div
        class="w-full h-full flex flex-col items-center overflow-hidden pb-20 bg-slate-200"
    >
        <!-- Trips Modal -->

        <!-- Add Driver Button -->
        <div class="w-full h-[10%] flex justify-end items-center px-2">
            <button
                class="w-50 rounded-md bg-primary text-white py-2 cursor-pointer hover:shadow-md"
                onclick={openModal}
            >
                Add Driver
            </button>
        </div>

        <!-- Drivers Table -->
        <div class="w-full h-full flex justify-center overflow-y-auto">
            <div class="w-[90%] h-[90%] shadow-2xl bg-white">
                <Table
                    columns={[
                        "name",
                        "email",
                        "phoneNumber",
                        "licenseNumber",
                        "address",
                        "idNumber",
                    ]}
                    data={driversStore.drivers}
                    actions={[
                        // {
                        //     iconClass: "fi fi-ts-comment-dots",
                        //     onClick: (row) => console.log("Edit", row),
                        // },
                        {
                            iconClass: "fi fi-ts-person-seat",
                            onClick: (row) => openAssignTripModal(row.id), // Pass driver ID to modal
                        },
                        {
                            iconClass: "fi fi-ts-earth-americas",
                            onClick: (row) => openTripsModal(row.id), // Pass driver ID to modal
                        },
                        {
                            iconClass: "fi fi-ts-trash-can-check",
                            onClick: (row) => openDeleteModal(row.id), // Pass driver ID to modal
                        },
                    ]}
                />
            </div>
        </div>
    </div>
</Layout>

<Modal show={showTripsModal} closeModal={closeTripModal}>
    <div>
        <TripDialog driverId={selectedDriverId} />
    </div>
</Modal>

<Modal show={showDeleteModal} closeModal={() => (showDeleteModal = false)}>
    <div class="w-full h-full flex flex-col items-start gap-5">
        <span class="text-white"
            >Are you sure you want to delete this driver?</span
        >
        <div class="w-full flex justify-end gap-5">
            <button
                class="cursor-pointer text-white px-4 py-2 rounded-md bg-primary"
                onclick={() => (showDeleteModal = false)}>Cancel</button
            >
            <button
                class="cursor-pointer text-white px-4 py-2 rounded-md bg-danger overflow-hidden min-w-fit w-24 flex justify-center items-center"
                onclick={deleteDriver}
            >
                {#if requesting}
                    <Loader />
                {:else}
                    Delete
                {/if}
            </button>
        </div>
    </div>
</Modal>

<!-- Assign Trip Modal -->
<Modal show={showAssignTripModal} closeModal={closeAssignTripModal}>
    <div>
        <AssignTrip driverId={selectedDriverId} />
    </div>
</Modal>

<!-- Add Driver Modal -->
<Modal show={showModal} {closeModal}>
    <NewDriverForm onComplete={closeModal} />
</Modal>
