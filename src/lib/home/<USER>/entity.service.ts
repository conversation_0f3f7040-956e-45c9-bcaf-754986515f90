import type {
    EntityDefinition,
    EntityInstance,
    EntityConfiguration
} from "../../types/entity.type";
import { entityDefinitionStore, entityInstanceStore } from "../../store/entity.store.svelte";
import { validateEntityData } from "../../shared/utils/entity.util";

// Entity Definition Services
export const entityDefinitionService = {
    // Get all entity definitions
    getAll(): EntityDefinition[] {
        return entityDefinitionStore.definitions;
    },

    // Get entity definition by ID
    getById(id: string): EntityDefinition | undefined {
        return entityDefinitionStore.getDefinition(id);
    },

    // Create new entity definition
    create(definition: Omit<EntityDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>): EntityDefinition {
        const newDefinition: EntityDefinition = {
            ...definition,
            id: `entity_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'current-user' // TODO: Get from auth context
        };

        entityDefinitionStore.addDefinition(newDefinition);
        return newDefinition;
    },

    // Update entity definition
    update(id: string, updates: Partial<EntityDefinition>): boolean {
        const existing = entityDefinitionStore.getDefinition(id);
        if (!existing) return false;

        entityDefinitionStore.updateDefinition(id, {
            ...updates,
            updatedAt: new Date().toISOString()
        });
        return true;
    },

    // Delete entity definition
    delete(id: string): boolean {
        const existing = entityDefinitionStore.getDefinition(id);
        if (!existing) return false;

        entityDefinitionStore.deleteDefinition(id);
        return true;
    },

    // Search entity definitions
    search(query: string): EntityDefinition[] {
        if (!query.trim()) return this.getAll();

        const lowerQuery = query.toLowerCase();
        return this.getAll().filter(entity =>
            entity.name.toLowerCase().includes(lowerQuery) ||
            entity.displayName.toLowerCase().includes(lowerQuery) ||
            entity.description?.toLowerCase().includes(lowerQuery)
        );
    },

    // Get entity configuration
    getConfiguration(id: string): EntityConfiguration | undefined {
        return entityDefinitionStore.getConfiguration(id);
    },

    // Set entity configuration
    setConfiguration(id: string, config: EntityConfiguration): boolean {
        const existing = entityDefinitionStore.getDefinition(id);
        if (!existing) return false;

        entityDefinitionStore.setConfiguration(id, config);
        return true;
    }
};

// Entity Instance Services
export const entityInstanceService = {
    // Get all instances for an entity
    getAll(entityId: string, includeDeleted = false): EntityInstance[] {
        return entityInstanceStore.getInstances(entityId, includeDeleted);
    },

    // Get instance by ID
    getById(entityId: string, instanceId: string): EntityInstance | undefined {
        return entityInstanceStore.getInstance(entityId, instanceId);
    },

    // Create new instance
    create(entityId: string, data: Record<string, any>): { success: boolean; instance?: EntityInstance; errors?: any } {
        const definition = entityDefinitionStore.getDefinition(entityId);
        if (!definition) {
            return { success: false, errors: { entity: 'Entity definition not found' } };
        }

        // Validate data against entity definition
        const validation = validateEntityData(definition, data);
        if (!validation.success) {
            return {
                success: false,
                errors: Object.fromEntries(
                    validation.error.errors.map(error => [error.path[0], error.message])
                )
            };
        }

        const instance = entityInstanceStore.addInstance(entityId, {
            entityId,
            data: validation.data,
            createdBy: 'current-user', // TODO: Get from auth context
            updatedBy: 'current-user'
        });

        return { success: true, instance };
    },

    // Update instance
    update(entityId: string, instanceId: string, data: Record<string, any>): { success: boolean; errors?: any } {
        const definition = entityDefinitionStore.getDefinition(entityId);
        if (!definition) {
            return { success: false, errors: { entity: 'Entity definition not found' } };
        }

        const existing = entityInstanceStore.getInstance(entityId, instanceId);
        if (!existing) {
            return { success: false, errors: { instance: 'Instance not found' } };
        }

        // Validate data against entity definition
        const validation = validateEntityData(definition, data);
        if (!validation.success) {
            return {
                success: false,
                errors: Object.fromEntries(
                    validation.error.errors.map(error => [error.path[0], error.message])
                )
            };
        }

        entityInstanceStore.updateInstance(entityId, instanceId, {
            data: validation.data,
            updatedBy: 'current-user' // TODO: Get from auth context
        });

        return { success: true };
    },

    // Delete instance
    delete(entityId: string, instanceId: string, softDelete = false): boolean {
        const existing = entityInstanceStore.getInstance(entityId, instanceId);
        if (!existing) return false;

        const definition = entityDefinitionStore.getDefinition(entityId);
        const shouldSoftDelete = softDelete || (definition?.softDelete ?? false);

        entityInstanceStore.deleteInstance(entityId, instanceId, shouldSoftDelete);
        return true;
    },

    // Search instances
    search(entityId: string, query: string): EntityInstance[] {
        const definition = entityDefinitionStore.getDefinition(entityId);
        if (!definition) return [];

        return entityInstanceStore.searchInstances(entityId, query, definition.searchableFields);
    },

    // Filter instances
    filter(entityId: string, filters: Record<string, any>): EntityInstance[] {
        const instances = this.getAll(entityId);

        return instances.filter(instance => {
            return Object.entries(filters).every(([field, value]) => {
                if (value === null || value === undefined || value === '') return true;

                const instanceValue = instance.data[field];

                // Handle different comparison types
                if (typeof value === 'object' && value.operator) {
                    switch (value.operator) {
                        case 'equals':
                            return instanceValue === value.value;
                        case 'notEquals':
                            return instanceValue !== value.value;
                        case 'contains':
                            return String(instanceValue).toLowerCase().includes(String(value.value).toLowerCase());
                        case 'startsWith':
                            return String(instanceValue).toLowerCase().startsWith(String(value.value).toLowerCase());
                        case 'endsWith':
                            return String(instanceValue).toLowerCase().endsWith(String(value.value).toLowerCase());
                        case 'greaterThan':
                            return Number(instanceValue) > Number(value.value);
                        case 'lessThan':
                            return Number(instanceValue) < Number(value.value);
                        case 'greaterThanOrEqual':
                            return Number(instanceValue) >= Number(value.value);
                        case 'lessThanOrEqual':
                            return Number(instanceValue) <= Number(value.value);
                        default:
                            return instanceValue === value.value;
                    }
                }

                // Simple equality check
                return instanceValue === value;
            });
        });
    },

    // Sort instances
    sort(instances: EntityInstance[], field: string, direction: 'asc' | 'desc' = 'asc'): EntityInstance[] {
        return [...instances].sort((a, b) => {
            const aValue = a.data[field];
            const bValue = b.data[field];

            if (aValue === bValue) return 0;

            let comparison = 0;
            if (aValue > bValue) comparison = 1;
            if (aValue < bValue) comparison = -1;

            return direction === 'desc' ? -comparison : comparison;
        });
    },

    // Paginate instances
    paginate(instances: EntityInstance[], page: number, pageSize: number): {
        items: EntityInstance[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
        hasNext: boolean;
        hasPrevious: boolean;
    } {
        const totalItems = instances.length;
        const totalPages = Math.ceil(totalItems / pageSize);
        const currentPage = Math.max(1, Math.min(page, totalPages));
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        return {
            items: instances.slice(startIndex, endIndex),
            totalItems,
            totalPages,
            currentPage,
            hasNext: currentPage < totalPages,
            hasPrevious: currentPage > 1
        };
    },

    // Get instance statistics
    getStats(entityId: string): {
        total: number;
        active: number;
        deleted: number;
        createdToday: number;
        createdThisWeek: number;
        createdThisMonth: number;
    } {
        const allInstances = entityInstanceStore.getInstances(entityId, true);
        const activeInstances = allInstances.filter(i => !i.deletedAt);
        const deletedInstances = allInstances.filter(i => i.deletedAt);

        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfWeek = new Date(startOfDay);
        startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        const createdToday = activeInstances.filter(i =>
            i.createdAt && new Date(i.createdAt) >= startOfDay
        ).length;

        const createdThisWeek = activeInstances.filter(i =>
            i.createdAt && new Date(i.createdAt) >= startOfWeek
        ).length;

        const createdThisMonth = activeInstances.filter(i =>
            i.createdAt && new Date(i.createdAt) >= startOfMonth
        ).length;

        return {
            total: allInstances.length,
            active: activeInstances.length,
            deleted: deletedInstances.length,
            createdToday,
            createdThisWeek,
            createdThisMonth
        };
    },

    // Export instances to JSON
    export(entityId: string, format: 'json' | 'csv' = 'json'): string {
        const instances = this.getAll(entityId);
        const definition = entityDefinitionStore.getDefinition(entityId);

        if (format === 'csv') {
            if (!definition) return '';

            const headers = definition.fields.map(f => f.label).join(',');
            const rows = instances.map(instance =>
                definition.fields.map(f => {
                    const value = instance.data[f.name];
                    return typeof value === 'string' && value.includes(',')
                        ? `"${value.replace(/"/g, '""')}"`
                        : value;
                }).join(',')
            );

            return [headers, ...rows].join('\n');
        }

        return JSON.stringify(instances, null, 2);
    }
};