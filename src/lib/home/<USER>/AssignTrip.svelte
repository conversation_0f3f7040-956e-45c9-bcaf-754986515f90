<script lang="ts">
    import { onMount } from "svelte";
    import type { Trip } from "../../types/trip.type";
    import {
        showErrorToast,
        showSuccessToast,
    } from "../../shared/utils/toast.util";
    import { assignTrip, getAvailableTrips } from "../service/trip.service";

    let loading = $state(false);
    let selectedTripId = $state("");
    let trips: Trip[] = $state([]);
    const { driverId } = $props();

    function loadTrips() {
        getAvailableTrips((response) => {
            trips = response.items;
        });
    }

    function assignTripToDriver() {
        if (!selectedTripId) {
            showErrorToast("Please select a trip to assign.");
            return;
        }

        assignTrip(selectedTripId, driverId, () => {
            loadTrips();
            selectedTripId = "";
            showSuccessToast("Trip assigned successfully.");
        });
    }

    onMount(() => {
        loadTrips();
    });
</script>

<div>
    <p class="text-white">Assign a trip to the driver</p>

    <div class="py-4">
        {#if loading}
            <div class="text-center py-4 text-white">Loading trips...</div>
        {:else if trips.length === 0}
            <div class="text-center py-4 text-white">No trips available</div>
        {:else}
            <select
                class="w-full border rounded-md p-2 bg-white"
                bind:value={selectedTripId}
            >
                <option value="" disabled>Select a trip</option>
                {#each trips as trip (trip.id)}
                    <option value={trip.id}>
                        {trip.client} - {trip.description.substring(0, 20)}
                    </option>
                {/each}
            </select>
        {/if}
    </div>

    <div class="flex justify-end gap-2 mt-4">
        <button
            class="rounded-md bg-primary text-white py-2 px-4 cursor-pointer hover:shadow-md"
            onclick={assignTripToDriver}
            disabled={!selectedTripId}
        >
            Assign
        </button>
    </div>
</div>
