import type { ApiResponse, Response } from "../../types/response.type";
import { deleteCall, get, post } from "../../shared/utils/network.util";
import type { User } from "../../types/user.type";
import type { NewDriverRequest } from "../../types/request.type";

export const addDriver = (
    driver: Partial<NewDriverRequest>,
    onSuccess: (response: User) => unknown,
    onComplete?: () => unknown
) => {
    post<Partial<NewDriverRequest>, User>(
        "/admin/drivers/add",
        driver,
        (response) => onSuccess(response),
        onComplete
    );
};

export const availableDrivers = (onSuccess: (response: ApiResponse<User>) => unknown) => {
    get<ApiResponse<User>>("/admin/drivers/available", (response) => onSuccess(response));
}

export const deleteDriver = (driverId: string, onSuccess: (response: Response) => unknown, onComplete?: () => unknown) => {
    deleteCall(`/admin/drivers/${driverId}`, onSuccess, onComplete);
}
export const getProfile = (onSuccess: (response: ApiResponse<User>) => unknown) => {
    get<ApiResponse<User>>("/user/me", (response) => onSuccess(response));
}