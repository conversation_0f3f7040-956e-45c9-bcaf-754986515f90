import { get, post, put } from "../../shared/utils/network.util";
import type { ApiResponse } from "../../types/response.type";
import type { Vehicle } from "../../types/vehicle.type";
import type { ChargingStation } from "../../types/chargingStation.type";
import type { User } from "../../types/user.type";
import type { Diagnostic } from "../../types/diagnostic.type";
import type { NewVehicleRequest } from "../../types/request.type";

export const getFleet = (onComplete: (response: ApiResponse<Vehicle>) => unknown) => {
    get<ApiResponse<Vehicle>>("/vehicles", (response) => onComplete(response));
}

export const getChargingStations = (onComplete: (response: ApiResponse<ChargingStation>) => unknown) => {
    get<ApiResponse<ChargingStation>>("/charging-station/all", (response) => onComplete(response));
}

export const getDrivers = (onComplete: (response: ApiResponse<User>) => unknown) => {
    get<ApiResponse<User>>("/admin/drivers", (response) => onComplete(response));
}

export const addVehicle = (vehicle: Partial<NewVehicleRequest>, onSuccess: (response: Vehicle) => unknown, onComplete?: () => unknown) => {
    post<Partial<NewVehicleRequest>, Vehicle>("/vehicles", vehicle, (response) => onSuccess(response), onComplete);
}

export const assignDriver = (vehicleId: string, driverId: string, onComplete: (response: ApiResponse<Vehicle>) => unknown) => {
    put<{}, ApiResponse<Vehicle>>(`/admin/drivers/assign`, { vehicleId, driverId }, (response) => onComplete(response));
}

export const removeDriver = (vehicleId: string, onComplete: (response: ApiResponse<Vehicle>) => unknown) => {
    put<{}, ApiResponse<Vehicle>>(`/admin/drivers/remove?vehicleId=${vehicleId}`, {}, (response) => onComplete(response));
}

export const getCarDiagnostics = (vehicleId: string, onSuccess: (response: ApiResponse<Diagnostic>) => unknown, onComplete?: () => unknown) => {
    get<ApiResponse<Diagnostic>>(`/diagnostic/${vehicleId}`, (response) => onSuccess(response), onComplete);
}

export const getVehicle = (vehicleId: string, onComplete: (response: ApiResponse<Vehicle>) => unknown) => {
    get<ApiResponse<Vehicle>>(`/vehicles/getById/${vehicleId}`, (response) => onComplete(response));
}