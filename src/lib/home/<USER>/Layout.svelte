<script lang="ts">
    import { push } from "svelte-spa-router";
    import Header from "./components/Header.svelte";

    let { children } = $props();
    let drawerOpen = $state(false);
</script>

<div class="w-screen h-screen flex flex-col items-center">
    <Header onMenuClicked={() => (drawerOpen = !drawerOpen)} />
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <div
        id="back-drop"
        class="modal-overlay fixed inset-0 flex items-center justify-center backdrop-brightness-50 bg-opacity-50 z-50"
        hidden={!drawerOpen}
        onclick={(event) => {
            if ((event.target as HTMLElement).id === "back-drop") {
                drawerOpen = false;
            }
        }}
    >
        <div
            class="w-[20%] h-full rounded-tr-md rounded-br-md absolute z-10 shadow-2xl left-0 top-0 bg-secondary px-2 py-2 flex flex-col gap-2"
            hidden={!drawerOpen}
        >
            <button
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
                onclick={() => push("/home")}
            >
                <span>Home</span>
            </button>
            <button
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
                onclick={() => push("/fleet")}
            >
                <span>Fleet</span>
            </button>
            <button
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
                onclick={() => push("/drivers")}
            >
                <span>Drivers</span>
            </button>
            <button
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
                onclick={() => push("/trips")}
            >
                <span>Trips</span>
            </button>
            <button
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
                onclick={() => push("/battery-reservations")}
            >
                <span>Reservations</span>
            </button>
            <button
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
                onclick={() => push("/entities")}
            >
                <span>Entities</span>
            </button>
            <div
                class="w-full h-10 hover:shadow-md hover:cursor-pointer hover:border hover:border-primary hover:rounded-md hover:text-primary text-white text-sm flex items-center px-1"
            >
                <span>Settings</span>
            </div>
        </div>
    </div>
    {@render children()}
</div>
