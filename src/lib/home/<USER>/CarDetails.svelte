<script lang="ts">
    import { onMount } from "svelte";
    import { io } from "socket.io-client";
    import { vehicleStore } from "../../store/vehicle.svelte";
    import type { Diagnostic } from "../../types/diagnostic.type";
    import { push } from "svelte-spa-router";
    import Layout from "./Layout.svelte";
    import { showErrorToast } from "../../shared/utils/toast.util";
    import Modal from "../../shared/components/Modal.svelte";
    import Loader from "../../shared/components/Loader.svelte";
    import { getCarDiagnostics } from "../service/fleet.service";
    import DtcView from "./components/DtcView.svelte";
    import GaugeView from "./components/GaugeView.svelte";
    import Map from "../../shared/components/Map.svelte";

    let mapComponent: Map | undefined = $state();
    let carIcon: google.maps.Marker | null = $state(null);
    let showDiagnosticDialog = $state(false);
    let showDialog = $state(false);
    let loadingDtc = $state(false);
    console.log(vehicleStore.vehicle);
    let soc = $state(
        vehicleStore.vehicle?.lastCondition?.batterySpecs[0].soc !== undefined
            ? vehicleStore.vehicle?.lastCondition?.batterySpecs[0].soc
            : 0,
    );
    let batteryTemperature = $state(
        vehicleStore.vehicle?.lastCondition?.batterySpecs[0]
            .batteryTemperature !== undefined
            ? vehicleStore.vehicle?.lastCondition?.batterySpecs[0]
                  .batteryTemperature
            : 0,
    );
    let motorTemperature = $state(
        vehicleStore.vehicle?.lastCondition?.motorTemperature !== undefined
            ? vehicleStore.vehicle?.lastCondition?.motorTemperature
            : 0,
    );
    let speed = $state(
        vehicleStore.vehicle?.lastCondition?.speed !== undefined
            ? vehicleStore.vehicle?.lastCondition?.speed
            : 0,
    );

    let checked = $state(true);
    let diagnostics: Diagnostic[] = $state([]);

    onMount(() => {
        const socket = io(import.meta.env.VITE_SOCKET_URL);
        socket.connect();

        socket.on("connect", () => console.log("connected to socketio"));
        socket.on("connect-error", () => {
            showErrorToast("Error when connecting to server");
        });
        socket.emit("register-client", vehicleStore.vehicle?.id);
        socket.on("diagnostic", (data) => {
            const diag = JSON.parse(data);
            diagnostics = [diag, ...diagnostics];
        });
        socket.on("location", (data) => {
            const jsonData = JSON.parse(data) as {
                latitude: number;
                longitude: number;
            };
            if (carIcon) {
                carIcon.setPosition({
                    lat: jsonData.latitude,
                    lng: jsonData.longitude,
                });
            }
            // Use the Map component's moveCamera method
            mapComponent?.moveCamera(jsonData.latitude, jsonData.longitude);
        });
        socket.on("metrics", (data) => {
            const jsonData = JSON.parse(data) as {
                speed: number;
                motorTemperature: number;
                batterySpecs: {
                    soc: number;
                    batteryTemperature: number;
                    voltage: number;
                    current: number;
                }[];
            };
            speed = jsonData.speed;
            soc = jsonData.batterySpecs[0].soc;
            motorTemperature = jsonData.motorTemperature;
            batteryTemperature = jsonData.batterySpecs[0].batteryTemperature;
        });
    });
</script>

<Layout>
    <div class="w-full h-full flex">
        <div class="h-full w-1/3 flex flex-col items-center justify-around">
            <div
                class="w-[95%] h-1/3 flex flex-col justify-center items-center rounded shadow-md bg-gray-100"
            >
                <div class="w-full flex justify-between px-5">
                    {#if speed === 0 && soc === 0 && batteryTemperature === 0 && motorTemperature === 0}
                        <span
                            class="inline-flex items-center bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900 dark:text-red-300"
                        >
                            <span class="w-2 h-2 me-1 bg-red-500 rounded-full"
                            ></span>
                            Disconnected
                        </span>
                    {:else}
                        <span
                            class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300"
                        >
                            <span class="w-2 h-2 me-1 bg-green-500 rounded-full"
                            ></span>
                            Connected
                        </span>
                    {/if}
                    <span>{vehicleStore.vehicle?.name}</span>

                    <div
                        class="flex flex-col items-center justify-center gap-2"
                    >
                        <button
                            onclick={(event) => {
                                event.preventDefault();
                                if (!checked) {
                                    showDialog = true;
                                } else {
                                    checked = !checked;
                                }
                            }}
                        >
                            <!-- <Switch bind:checked  /> -->
                        </button>
                        <button
                            class="flex justify-center cursor-pointer"
                            aria-label="diagnostic"
                            onclick={() => {
                                showDiagnosticDialog = true;
                                loadingDtc = true;
                                getCarDiagnostics(
                                    vehicleStore.vehicle!.id,
                                    (response) =>
                                        (diagnostics = response.items),
                                    () => (loadingDtc = false),
                                );
                            }}
                        >
                            <i class="fas fa-tools ml-3"></i>
                        </button>
                    </div>
                </div>
                <img src="/images/car-exp.png" alt="car-logo" class="h-[70%]" />
            </div>

            <div class="w-full h-1/4 flex justify-around">
                <GaugeView
                    value={speed}
                    min={0}
                    max={100}
                    text={`${speed} Kmph`}
                    img="/images/speed.png"
                />
                <GaugeView
                    value={motorTemperature}
                    min={0}
                    max={200}
                    text={`${motorTemperature} °C`}
                    img="/images/temp.png"
                />
            </div>
            <button
                class="text-sm font-semibold text-secondary hover:bg-secondary rounded-lg border border-secondary px-2 py-1 mb-2 cursor-pointer duration-200 transition-all hover:text-white"
                onclick={() => push("/battery-info")}>Battery</button
            >

            <div class="w-full h-1/4 flex justify-around">
                <GaugeView
                    value={soc}
                    min={0}
                    max={100}
                    text={`${soc} %`}
                    img="/images/charging.png"
                />
                <GaugeView
                    value={batteryTemperature}
                    min={0}
                    max={200}
                    text={`${batteryTemperature} °C`}
                    img="/images/thermometer.png"
                />
            </div>
        </div>
        <Map
            bind:this={mapComponent}
            showVehicles={false}
            showChargingStations={false}
            showBatteries={false}
            loadData={false}
            initialCenter={vehicleStore.vehicle?.lastSeen}
            on:mount={(e) => {
                // Initialize the car marker after the map is loaded
                carIcon = new google.maps.Marker({
                    map: e.detail.map,
                    icon: "/images/car.png",
                });

                // Set initial position if available
                if (vehicleStore.vehicle?.lastSeen) {
                    carIcon.setPosition({
                        lat: vehicleStore.vehicle.lastSeen.latitude,
                        lng: vehicleStore.vehicle.lastSeen.longitude,
                    });
                }
            }}
        />
    </div>
</Layout>

<Modal closeModal={() => (showDialog = false)} show={showDialog}>
    <div class="w-full flex flex-col px-5 py-5 gap-10">
        <span>Are you sure you want to cut the system?</span>
        <div class="w-full flex justify-end gap-2">
            <button
                class="rounded bg-red-500 text-white p-2 cursor-pointer"
                onclick={() => {
                    checked = !checked;
                    showDialog = false;
                }}>Cancel</button
            >
            <button
                class="rounded bg-green-500 text-white p-2 cursor-pointer"
                onclick={() => (showDialog = false)}>Confirm</button
            >
        </div>
    </div>
</Modal>

<Modal
    closeModal={() => (showDiagnosticDialog = false)}
    show={showDiagnosticDialog}
>
    <div class="w-full flex flex-col px-5 py-5 gap-10 overflow-hidden">
        <span class="text-white font-medium text-xl">Diagnostic Messages</span>
        {#if loadingDtc}
            <div class="flex w-full justify-center">
                <Loader />
            </div>
        {:else}
            <DtcView dtcs={diagnostics} />
        {/if}
    </div>
</Modal>
