import { get, post, put } from "../../shared/utils/network.util";
import type { ApiResponse } from "../../types/response.type";
import type { Trip } from "../../types/trip.type";

export const getDriverTrips = (driverId: string, onSuccess: (response: Trip[]) => unknown, onComplete?: () => unknown) => {
    get<Trip[]>(`/trip/driver?driver_id=${driverId}`, (response) => onSuccess(response));
}
export const getTrips = (onComplete: (response: ApiResponse<Trip>) => unknown) => {
    get<ApiResponse<Trip>>("/trip", (response) => onComplete(response));
}
export const addTrip = (trip: Trip, onComplete: (response: Trip) => unknown) => {
    post<Trip, Trip>("/trip", trip, (response) => onComplete(response));
}
export const getAvailableTrips = (onComplete: (response: ApiResponse<Trip>) => unknown) => {
    get<ApiResponse<Trip>>("/trip/available", (response) => onComplete(response));
}
export const assignTrip = (tripId: string, driverId: string, onComplete: (response: ApiResponse<Trip>) => unknown) => {
    put<{}, ApiResponse<Trip>>(`/trip/assign?tripId=${tripId}&driverId=${driverId}`, {}, (response) => onComplete(response));
}