<script lang="ts">
    import Modal from "../../shared/components/Modal.svelte";
    import Table from "../../shared/components/Table.svelte";
    import { fleetStore, vehicleStore } from "../../store/vehicle.svelte";
    import type { Vehicle } from "../../types/vehicle.type";
    import Layout from "./Layout.svelte";
    import {
        assignDriver,
        getFleet,
        removeDriver,
    } from "../service/fleet.service";
    import { onMount } from "svelte";
    import { availableDrivers } from "../service/driver.service";
    import type { User } from "../../types/user.type";
    import {
        showErrorToast,
        showSuccessToast,
        showWarningToast,
    } from "../../shared/utils/toast.util";
    import { push } from "svelte-spa-router";
    import NewVehicleForm from "./components/NewVehicleForm.svelte";

    let showModal = $state(false);
    let showAssignModal = $state(false);
    let showDeleteModal = $state(false);
    let vehicle: Vehicle & { selectedDriverId?: string } = $state(
        {},
    ) as Vehicle;
    let drivers: User[] = $state([]);

    const navigateToReserveBattery = (vehicleId: string) => {
        push(`/reserve-battery/${vehicleId}`);
    };

    const closeModal = () => {
        showModal = false;
    };

    const openModal = () => {
        showModal = true;
    };

    const openAssignModal = (selectedVehicle: Vehicle) => {
        vehicle = selectedVehicle;
        showAssignModal = true;
    };
    const openDeleteModal = (selectedVehicle: Vehicle) => {
        vehicle = selectedVehicle;
        showDeleteModal = true;
    };

    const closeAssignModal = () => {
        showAssignModal = false;
    };

    onMount(async () => {
        // await checkPaymentStatus();

        availableDrivers((response) => {
            drivers = response.items;
        });
    });
</script>

<Layout>
    <div
        class="w-full h-full flex flex-col items-center overflow-hidden pb-20 bg-slate-200"
    >
        <div class="w-full h-[10%] flex justify-end items-center px-2">
            <button
                onclick={openModal}
                class="w-50 rounded-md bg-primary text-white py-2 cursor-pointer hover:shadow-md"
            >
                Add vehicle
            </button>
        </div>
        <div class="w-full h-full flex justify-center overflow-y-auto">
            <div class="w-[90%] h-full max-h-[90%] shadow-2xl bg-white">
                <Table
                    columns={["name", "make", "plateNumber", "serialNumber"]}
                    data={fleetStore.fleet}
                    rowClick={(row) => {
                        if (row.lastSeen !== null) {
                            vehicleStore.vehicle = row;
                            push(`/car`);
                        }
                    }}
                    actions={[
                        {
                            iconClass: "fi fi-ts-car-battery",
                            onClick: (row) => navigateToReserveBattery(row.id),
                        },
                        {
                            iconClass: "fi fi-ts-circle-user",
                            onClick: (row) => openAssignModal(row),
                        },
                        {
                            iconClass: "fi fi-ts-trash-can-check",
                            onClick: (row) => openDeleteModal(row),
                        },
                    ]}
                />
            </div>
        </div>
    </div>
</Layout>

<Modal show={showAssignModal} closeModal={closeAssignModal}>
    {#if vehicle.state === "IN_SHOP"}
        <select
            bind:value={vehicle.selectedDriverId}
            class="border rounded-md p-2 mr-2 text-white bg-secondary"
        >
            <option value="">Select a driver</option>
            {#each drivers as driver (driver.id)}
                <option value={driver.id}>{driver.name}</option>
            {/each}
        </select>
        <button
            class="w-1/3 rounded-md bg-primary text-white py-2 cursor-pointer hover:shadow-md"
            onclick={() => {
                if (vehicle.selectedDriverId) {
                    assignDriver(vehicle.id, vehicle.selectedDriverId, (_) => {
                        showSuccessToast("Driver assigned successfully");
                        fleetStore.fleet = [];
                        getFleet((response) => {
                            fleetStore.fleet = response.items;
                        });
                        closeModal();
                    });
                }
            }}
        >
            Assign
        </button>
    {:else}
        <button
            class="w-1/3 rounded-md bg-primary text-white py-2 cursor-pointer hover:shadow-md"
            onclick={() => {
                removeDriver(vehicle.id, (response) => {
                    showErrorToast("Driver removed successfully");
                    fleetStore.fleet = [];
                    getFleet((response) => {
                        fleetStore.fleet = response.items;
                    });
                    closeModal();
                });
            }}
        >
            Remove Driver
        </button>
    {/if}
</Modal>
<Modal show={showDeleteModal} closeModal={() => (showDeleteModal = false)}>
    <div class="w-full h-full flex flex-col items-start gap-5">
        <span class="text-white"
            >Are you sure you want to delete this vehicle?</span
        >
        <div class="w-full flex justify-end gap-5">
            <button
                class="cursor-pointer text-white px-4 py-2 rounded-md bg-primary"
                onclick={() => (showDeleteModal = false)}>Cancel</button
            >
            <button
                class="cursor-pointer text-white px-4 py-2 rounded-md bg-tertiery"
                >Delete</button
            >
        </div>
    </div>
</Modal>

<Modal show={showModal} {closeModal}>
    <NewVehicleForm onComplete={closeModal} />
</Modal>
