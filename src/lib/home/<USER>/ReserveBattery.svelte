<script lang="ts">
    import Modal from "../../shared/components/Modal.svelte";
    import Layout from "./Layout.svelte";
    import type { Battery } from "../../types/battery.type";
    import { params } from "svelte-spa-router";
    import { get } from "svelte/store";
    import Map from "../../shared/components/Map.svelte";

    let selectedBattery: Battery | null = null;
    let showModal = false;
    let isProcessingPayment = false;
    let paymentError = "";
    let token = localStorage.getItem("access_token") || "";
    let mapComponent;

    function closeModal() {
        showModal = false;
    }

    async function handleReserveBattery() {
        if (!selectedBattery) return;
        
        const vehicleId = get(params)?.vehicleId;
        if (!vehicleId) {
            console.error("Vehicle ID is undefined");
            return;
        }
        
        try {
            isProcessingPayment = true;
            
            const amount = 10000;
            
            const response = await fetch(`${import.meta.env.VITE_SERVER_URL}/payment/create-checkout-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({
                    batteryId: selectedBattery.id,
                    vehicleId: vehicleId,
                    amount: amount
                }),
            });
            
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            
            const data = await response.json();
            
            window.location.href = data.checkoutUrl;
            
        } catch (error) {
            paymentError = (error as any).message || "Payment processing failed";
            console.error('Payment error:', paymentError);
        } finally {
            isProcessingPayment = false;
        }
    }
</script>

<Layout>
    <Map
        bind:this={mapComponent}
        showVehicles={false}
        showChargingStations={false}
        showBatteries={true}
        loadData={true}
        onMarkerClick={(item, type) => {
            if (type === 'battery') {
                selectedBattery = item as Battery;
                showModal = true;
            }
        }}
    />

    <Modal show={showModal} closeModal={closeModal}>
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-white text-2xl font-bold">Battery Details</h2>
            </div>
    
            {#if selectedBattery}
                <!-- Battery visual indicator -->
                <div class="mb-6 flex justify-center">
                    <div class="relative w-32 h-16 border-2 border-gray-600 rounded-md bg-gray-700">
                        <div class="absolute top-0 left-0 bottom-0 w-30 bg-green-500 rounded-l opacity-60"></div>
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white font-bold">
                            100%
                        </div>
                        <div class="absolute right-0 top-0 bottom-0 w-3 h-full border-l-2 border-gray-600 bg-gray-700 rounded-r"></div>
                    </div>
                </div>
    
                <!-- Organized info into categories -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-white mb-6">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-2 text-blue-300">General Information</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-300">Manufacturer:</span>
                                <span class="font-medium">{selectedBattery.manufacturer}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Type:</span>
                                <span class="font-medium">{selectedBattery.typeDesignation}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Serial Number:</span>
                                <span class="font-medium">{selectedBattery.serialNumber}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Chemistry:</span>
                                <span class="font-medium">{selectedBattery.cellChemistry}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Weight:</span>
                                <span class="font-medium">{selectedBattery.weight}</span>
                            </div>
                        </div>
                    </div>
    
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-2 text-blue-300">Technical Specs</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-300">Total Voltage:</span>
                                <span class="font-medium">{selectedBattery.totalVoltageMin} - {selectedBattery.totalVoltageMax}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Nominal Voltage:</span>
                                <span class="font-medium">{selectedBattery.nominalVoltage}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Capacity:</span>
                                <span class="font-medium">{selectedBattery.nominalCapacity}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Cells:</span>
                                <span class="font-medium">{selectedBattery.numberOfCells}</span>
                            </div>
                        </div>
                    </div>
    
                    <div class="bg-gray-700 p-4 rounded-lg md:col-span-2">
                        <h3 class="text-lg font-semibold mb-2 text-blue-300">Purchase & Warranty</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-300">Manufacture Date:</span>
                                <span class="font-medium">{selectedBattery.manufactureDate}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Sale Date:</span>
                                <span class="font-medium">{selectedBattery.saleDate}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">Warranty Period:</span>
                                <span class="font-medium">{selectedBattery.warrantyPeriod} years</span>
                            </div>
                        </div>
                    </div>
                </div>
    
                <!-- Payment options with improved styling -->
                <div class="mt-6">
                    <h3 class="text-lg font-semibold text-white mb-3">Reserve This Battery</h3>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <!-- Stripe Button -->
                        <button 
                            class="flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg transition-all duration-200 disabled:opacity-50 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 focus:outline-none"
                            onclick={handleReserveBattery}
                            disabled={isProcessingPayment}
                        >
                            {#if isProcessingPayment}
                                Processing...
                            {:else}
                                Reserve Battery
                            {/if}
                        </button>
                    </div>
                    
                    {#if paymentError}
                        <div class="mt-3 text-red-400 text-sm">
                            {paymentError}
                        </div>
                    {/if}
                </div>
            {/if}
    </Modal>
</Layout>
