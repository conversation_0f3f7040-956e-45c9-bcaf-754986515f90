<script lang="ts">
    import { onMount, tick } from "svelte";
    import type { BatteryReservation } from "../../types/reservation.type";
    import { getReservations, cancelReservation, cancelReservationWithRefund } from "../service/battery.service";
    import { reservationStore } from "../../store/reservation.svelte";
    import Table from "../../shared/components/Table.svelte";
    import Layout from "./Layout.svelte";
    import Modal from "../../shared/components/Modal.svelte";

    let reservations: BatteryReservation[] = $state([]);
    let displayReservations: BatteryReservation[] = $state([]);
    let isLoading = $state(true);
    let error: string | null = $state(null);
    let successMessage: string | null = $state(null);
    
    // Refund modal state
    let showRefundModal = $state(false);
    let selectedReservation: BatteryReservation | null = $state(null);
    let refundReason = $state("requested_by_customer");
    let refundModalLoading = $state(false);
    let refundModalError: string | null = $state(null);

    // Filter state
    let searchQuery = $state("");
    let selectedVehicle = $state("all");
    let selectedPaymentStatus = $state("all");

    const loadReservations = async () => {
        try {
            isLoading = true;
            error = null;
            await getReservations(async (response) => {
                reservationStore.reservations = response.items;
                console.log(reservationStore.reservations);
                              
                reservations = reservationStore.reservations.map(r => ({
                    ...r,
                    vehicleSerialNumber: r.vehicle?.serialNumber || "Unknown"
                }));
                displayReservations = [...reservations];
                await tick();
            });
        } catch (err) {
            error = "Failed to load reservations. Please try again.";
            console.error(err);
        } finally {
            isLoading = false;
        }
    };

    const openRefundModal = (reservation: BatteryReservation) => {
        selectedReservation = reservation;
        refundReason = "requested_by_customer";
        refundModalError = null;
        showRefundModal = true;
    };

    const closeRefundModal = () => {
        showRefundModal = false;
        selectedReservation = null;
        refundModalError = null;
    };

    const processCancelWithRefund = async () => {
        if (!selectedReservation || !selectedReservation.id || !selectedReservation.paymentSessionId) {
            console.log(selectedReservation);
            console.log(selectedReservation!.id);
            console.log(selectedReservation!.paymentSessionId);
            
            
            
            refundModalError = "Missing reservation or payment information";
            return;
        }

        refundModalLoading = true;
        refundModalError = null;

        try {
            cancelReservationWithRefund(
                selectedReservation.id,
                selectedReservation.paymentSessionId,
                refundReason,
                () => {
                    closeRefundModal();
                    successMessage = "Battery reservation cancelled and refund processed successfully!";
                    loadReservations();
                    
                    setTimeout(() => {
                        successMessage = null;
                    }, 5000);
                },
                () => {
                    refundModalLoading = false;
                }
            );
        } catch (err) {
            refundModalError = "Failed to process refund. Please try again.";
            console.error(err);
        } finally {
            refundModalLoading = false;
        }
    };

    const filterReservations = (query: string) => {
        searchQuery = query;
        applyFilters();
    };

    const filterByVehicle = (vehicleSerial: string) => {
        selectedVehicle = vehicleSerial;
        applyFilters();
    };

    const filterByPaymentStatus = (status: string) => {
        selectedPaymentStatus = status;
        applyFilters();
    };

    const applyFilters = () => {
        let filtered = [...reservations];
        
        // Apply search query filter
        if (searchQuery) {
            filtered = filtered.filter(
                (r) =>
                    (r.battery?.serialNumber?.toLowerCase().includes(searchQuery) || false) ||
                    (r.vehicle.serialNumber?.toLowerCase().includes(searchQuery) || false)
            );
        }
        
        // Apply vehicle filter
        if (selectedVehicle !== "all") {
            filtered = filtered.filter(r => r.vehicle.serialNumber === selectedVehicle);
        }
        
        // Apply payment status filter
        if (selectedPaymentStatus !== "all") {
            filtered = filtered.filter(r => 
                r.paymentStatus?.toLowerCase() === selectedPaymentStatus.toLowerCase()
            );
        }
        
        displayReservations = filtered;
    };

    onMount(() => {
        loadReservations();        
    });
</script>

<Layout>
    <div class="flex flex-col mt-6 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold text-gray-800">Battery Reservations</h2>
                </div>

                {#if successMessage}
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded mb-4" role="alert">
                        <p>{successMessage}</p>
                    </div>
                {/if}

                {#if isLoading}
                    <div class="flex justify-center items-center py-12">
                        <div
                            class="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"
                        ></div>
                    </div>
                {:else if error}
                    <div
                        class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded mb-4"
                        role="alert"
                    >
                        <p>{error}</p>
                        <button
                            onclick={loadReservations}
                            class="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Retry
                        </button>
                    </div>
                {:else}
                    <div class="mb-4 flex flex-col sm:flex-row gap-4">
                        <input
                            type="text"
                            placeholder="Search by serial number or vehicle serial..."
                            class="w-full sm:w-64 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            oninput={(e) => {
                                const query = (e.target as HTMLInputElement).value.toLowerCase();
                                filterReservations(query);
                            }}
                        />
                        <select
                            class="w-full sm:w-48 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            onchange={(e) => {
                                const value = (e.target as HTMLInputElement).value;
                                filterByVehicle(value);
                            }}
                        >
                            <option value="all">All Vehicles</option>
                            {#each [...new Set(reservations.map(r => r.vehicle.serialNumber).filter(s => s !== undefined && s !== "Unknown"))] as serial}
                                <option value={serial}>{serial}</option>
                            {/each}
                        </select>
                        <select
                            class="w-full sm:w-48 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            onchange={(e) => {
                                const value = (e.target as HTMLInputElement).value;
                                filterByPaymentStatus(value);
                            }}
                        >
                            <option value="all">All Payment Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="paid">Paid</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <Table
                        data={displayReservations}
                        columns={["battery.serialNumber", "vehicleSerialNumber"]}
                        actions={[
                        ]}
                    />
                {/if}
            </div>
        </div>
    </div>


        <Modal show={showRefundModal} closeModal={closeRefundModal} >
            <h3 class="text-lg font-semibold text-white">Cancel Reservation with Refund</h3>
            
            <div>
                <p class="mb-4 text-white">
                    Are you sure you want to cancel the reservation for battery 
                    <span class="font-semibold text-white">{selectedReservation!.battery?.serialNumber || "Unknown"}</span> 
                    and process a refund?
                </p>
                
                {#if refundModalError}
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-3 rounded mb-4">
                        {refundModalError}
                    </div>
                {/if}
                
                <div class="mb-4">
                    <label for="" class="block text-sm font-medium text-white mb-1">
                        Refund Reason
                    </label>
                    <select
                        bind:value={refundReason}
                        class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="requested_by_customer">Requested by Customer</option>
                        <option value="defective_battery">Defective Battery</option>
                        <option value="incompatible_vehicle">Incompatible with Vehicle</option>
                        <option value="service_unavailable">Service Unavailable</option>
                        <option value="changed_mind">Customer Changed Mind</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end gap-3">
                <button
                    type="button"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    onclick={closeRefundModal}
                    disabled={refundModalLoading}
                >
                    Cancel
                </button>
                <button
                    type="button"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    onclick={processCancelWithRefund}
                    disabled={refundModalLoading}
                >
                    {#if refundModalLoading}
                        <span class="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                    {/if}
                    Process Refund
                </button>
            </div>
        </Modal>
</Layout>

<style>
    :global(.table-row) {
        transition: background-color 0.2s ease;
    }
    :global(.table-row:hover) {
        background-color: #f9fafb;
    }
    :global(.action-button) {
        position: relative;
        cursor: pointer;
    }
    :global(.action-button:hover::after) {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #1f2937;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 10;
    }
</style>
