<script lang="ts">
    import Layout from "./Layout.svelte";
    import Modal from "../../shared/components/Modal.svelte";

    import { Loader } from "@googlemaps/js-api-loader";
    import type { Trip } from "../../types/trip.type";
    import { tripStore } from "../../store/trip.svelte";
    import { addTrip, getTrips } from "../service/trip.service";
    import Table from "../../shared/components/Table.svelte";
    import { onMount } from "svelte";
    import Input from "../../shared/components/Input.svelte";

    let loading = $state(false);
    let mapContainer: HTMLElement;
    let searchInput: HTMLInputElement;
    let map: google.maps.Map | null = null;
    let marker: google.maps.Marker | null = null;
    let geocoder: google.maps.Geocoder | null = null;
    let autocomplete: google.maps.places.Autocomplete | null = null;
    let mapLoading = $state(false);
    let mapError = "";
    let showModal = $state(false); // Modal visibility state

    let trip: Trip = $state({
        startingPoint: { latitude: 0, longitude: 0 },
        destination: { latitude: 0, longitude: 0 },
        destinationAddress: "",
        startingAddress: "",
        duration: 0,
        distance: 0,
        vehicleId: "",
        driverId: "",
        client: "",
        description: "",
        createdAt: new Date().toISOString(),
        dueDate: "",
    });

    let selectedPlace: {
        address: string;
        latitude: number;
        longitude: number;
        isValid: boolean;
    } | null = null;

    const openModal = () => {
        showModal = true;
        setTimeout(initializeMap, 0); // Ensure map initializes when modal opens
    };

    const closeModal = () => {
        showModal = false;
    };

    const initializeMap = async () => {
        if (!mapContainer) {
            console.error("Map container not found");
            return;
        }

        try {
            mapLoading = true;
            const loader = new Loader({
                apiKey: "AIzaSyAJ17kW-KH0sEhOx5fWl0WJBJBr3toUb4Q",
                version: "weekly",
            });

            await loader.importLibrary("places");
            await loader.load();

            map = new google.maps.Map(mapContainer, {
                center: { lat: 0, lng: 0 },
                zoom: 2,
                streetViewControl: false,
            });

            geocoder = new google.maps.Geocoder();

            if (searchInput) {
                autocomplete = new google.maps.places.Autocomplete(
                    searchInput,
                    {
                        types: ["address"],
                        fields: ["formatted_address", "geometry", "name"],
                        componentRestrictions: { country: "tn" },
                    },
                );

                autocomplete.addListener("place_changed", async () => {
                    const place = autocomplete?.getPlace();

                    if (!place?.geometry) {
                        const validatedPlace = await validateAddress(
                            searchInput.value,
                        );
                        if (validatedPlace.isValid) {
                            updateMarkerPosition(
                                new google.maps.LatLng(
                                    validatedPlace.latitude,
                                    validatedPlace.longitude,
                                ),
                            );
                            handleDestinationSelect(
                                validatedPlace.latitude,
                                validatedPlace.longitude,
                                validatedPlace.address,
                            );
                        } else {
                            mapError =
                                "Please select a valid address from the dropdown";
                            clearSelection();
                        }
                        return;
                    }

                    const location = place.geometry.location;
                    if (location) {
                        updateMarkerPosition(location);
                        handleDestinationSelect(
                            location.lat(),
                            location.lng(),
                            place.formatted_address || searchInput.value,
                        );
                    }
                });
            }

            marker = new google.maps.Marker({
                map: map,
                visible: false,
                draggable: true,
            });

            setupMapListeners();

            mapLoading = false;
        } catch (err) {
            console.error("Error initializing map:", err);
            mapError = "Failed to load Google Maps";
            mapLoading = false;
        }
    };

    const add = () => {
        addTrip(trip, (response) => {
            tripStore.trips.push(response);
            closeModal(); // Close modal after adding trip
            trip = {} as Trip;
        });
    };

    const formatTripData = () => {
        return tripStore.trips.map((trip) => ({
            client: trip.client,
            destination: trip.destinationAddress,
            description: trip.description,
            dueDate: trip.dueDate,
            status: trip.status,
        }));
    };

    const validateAddress = async (address: string) => {
        if (!geocoder) {
            return { address: "", latitude: 0, longitude: 0, isValid: false };
        }

        try {
            const result = await geocoder.geocode({ address });
            if (result.results[0]) {
                const location = result.results[0].geometry.location;
                return {
                    address: result.results[0].formatted_address,
                    latitude: location.lat(),
                    longitude: location.lng(),
                    isValid: true,
                };
            }
        } catch (err) {
            console.error("Address validation error:", err);
        }

        return { address: "", latitude: 0, longitude: 0, isValid: false };
    };

    const handleDestinationSelect = (
        lat: number,
        lng: number,
        address: string,
    ) => {
        trip.destination = {
            latitude: lat,
            longitude: lng,
        };
        trip.destinationAddress = address;

        if (searchInput) {
            searchInput.value = address;
        }
    };

    const updateMarkerPosition = (latLng: google.maps.LatLng) => {
        if (marker) {
            marker.setPosition(latLng);
            marker.setVisible(true);
        }
    };

    const clearSelection = () => {
        if (searchInput) {
            searchInput.value = "";
        }
        if (marker) {
            marker.setVisible(false);
        }
        selectedPlace = null;
    };

    const setupMapListeners = () => {
        if (!map || !marker) return;

        map.addListener("click", async (e: google.maps.MapMouseEvent) => {
            if (e.latLng) {
                updateMarkerPosition(e.latLng);

                try {
                    const result = await geocoder?.geocode({
                        location: e.latLng,
                    });
                    const formattedAddress =
                        result?.results[0]?.formatted_address ||
                        "Selected Location";

                    handleDestinationSelect(
                        e.latLng.lat(),
                        e.latLng.lng(),
                        formattedAddress,
                    );
                } catch (err) {
                    console.error("Geocoding error:", err);
                    handleDestinationSelect(
                        e.latLng.lat(),
                        e.latLng.lng(),
                        "Selected Location",
                    );
                }
            }
        });

        marker.addListener("dragend", async () => {
            const latLng = marker?.getPosition();
            if (latLng) {
                try {
                    const result = await geocoder?.geocode({
                        location: latLng,
                    });
                    const formattedAddress =
                        result?.results[0]?.formatted_address ||
                        "Dragged Location";

                    handleDestinationSelect(
                        latLng.lat(),
                        latLng.lng(),
                        formattedAddress,
                    );
                } catch (err) {
                    console.error("Geocoding error:", err);
                    handleDestinationSelect(
                        latLng.lat(),
                        latLng.lng(),
                        "Dragged Location",
                    );
                }
            }
        });
    };

    onMount(async () => {
        getTrips(async (response) => {
            tripStore.trips = response.items;
        });
    });
</script>

<Layout>
    <div
        class="w-full h-full flex flex-col items-center overflow-hidden pb-20 bg-slate-200"
    >
        <div class="w-full h-[10%] flex justify-end items-center px-2">
            <button
                onclick={openModal}
                class="w-50 rounded-md bg-primary text-white py-2 cursor-pointer hover:shadow-md"
            >
                Add trip
            </button>
        </div>
        <div class="w-full h-full flex justify-center overflow-y-auto">
            {#if loading}
                <div class="text-center py-4">Loading trips...</div>
            {:else}
                <div class="w-[90%] h-full max-h-[90%] shadow-2xl bg-white">
                    <Table
                        columns={[
                            "client",
                            "destination",
                            "description",
                            "dueDate",
                            "status",
                        ]}
                        data={formatTripData()}
                        rowClick={(trip) => {
                            console.log(trip);
                        }}
                    />
                </div>
            {/if}
        </div>
    </div>
</Layout>

<!-- Modal Component -->
<Modal show={showModal} {closeModal}>
    <div class="sm:max-w-[600px] h-[90vh] overflow-y-auto p-4">
        <h2 class="text-xl font-bold mb-4 text-white">Add Trip</h2>
        <p class="text-sm text-white">
            Add a new trip to your fleet. Click save when you're done.
        </p>

        <div class="grid gap-4">
            <div>
                <label for="destination" class="text-left text-white"
                    >Destination</label
                >
                <div class="space-y-4">
                    <div class="relative">
                        <input
                            bind:this={searchInput}
                            type="text"
                            placeholder="Search for an address"
                            class="w-full border rounded p-2 text-white"
                        />
                        <button
                            class="absolute right-1 top-1/2 -translate-y-1/2 px-2 py-1 h-7 bg-gray-300 rounded cursor-pointer"
                            onclick={() => (searchInput.value = "")}
                        >
                            Clear
                        </button>
                    </div>

                    <div
                        bind:this={mapContainer}
                        class="w-full h-[300px] rounded-lg border"
                    ></div>

                    {#if mapLoading}
                        <div
                            class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50"
                        >
                            Loading map...
                        </div>
                    {/if}
                </div>
            </div>

            <label for="client" class="text-left text-white">Client</label>
            <Input
                type="text"
                bind:value={trip.client}
                placeholder="Enter client name"
            />

            <label for="description" class="text-left text-white"
                >Description</label
            >
            <Input
                type="text"
                bind:value={trip.description}
                placeholder="Enter trip description"
            />
            <label for="dueDate" class="text-left text-white">Due Date</label>
            <input
                type="date"
                id="dueDate"
                bind:value={trip.dueDate}
                class="w-full border rounded p-2 text-white"
            />
        </div>
        <div class="flex justify-end mt-4">
            <button
                class="rounded-md bg-primary text-white py-2 px-4 cursor-pointer hover:shadow-md"
                onclick={add}
            >
                Save changes
            </button>
        </div>
    </div>
</Modal>
