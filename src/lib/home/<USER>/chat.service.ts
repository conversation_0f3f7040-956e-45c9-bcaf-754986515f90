import { get, post } from "../../shared/utils/network.util";
import type { ChatMessage } from "../../types/chatMessage.type";
import type { ApiResponse } from "../../types/response.type";

export const loadMessages = (senderId: string, receiverId: string, onComplete: (response: ApiResponse<ChatMessage[]>) => unknown) => {
    get<ApiResponse<ChatMessage[]>>(`/chatmessages/all?senderId=${senderId}&receiverId=${receiverId}`, (response) => onComplete(response));
};

export const initializeChat = (userId: string, driverId: string, onComplete: (response: ApiResponse<string>) => unknown) => {
    get<ApiResponse<string>>(`/chatrooms/${userId}/${driverId}`, (response) => onComplete(response));
};

export const sendMessage = (message: Omit<ChatMessage, "id">, onComplete: (response: ApiResponse<ChatMessage>) => unknown) => {
    post<Omit<ChatMessage, "id">, ApiResponse<ChatMessage>>("/chatmessages/send", message, (response) => onComplete(response));
};
