<script lang="ts">
    import { onMount } from "svelte";
    import { io } from "socket.io-client";
    import { vehicleStore } from "../../store/vehicle.svelte";
    import Layout from "./Layout.svelte";
    import Gauge from "svelte-gauge";
    import { showErrorToast } from "../../shared/utils/toast.util";
    import Map from "../../shared/components/Map.svelte";

    let mapComponent: Map | undefined = $state();
    let carIcon: google.maps.Marker | null = $state(null);
    let soc = $state(
        vehicleStore.vehicle?.lastCondition?.batterySpecs[0].soc !== undefined
            ? vehicleStore.vehicle?.lastCondition?.batterySpecs[0].soc
            : 0,
    );
    let batteryTemperature = $state(
        vehicleStore.vehicle?.lastCondition?.batterySpecs[0]
            .batteryTemperature !== undefined
            ? vehicleStore.vehicle?.lastCondition?.batterySpecs[0]
                  .batteryTemperature
            : 0,
    );
    let voltage = $state(
        vehicleStore.vehicle?.lastCondition?.batterySpecs[0].voltage !==
            undefined
            ? vehicleStore.vehicle?.lastCondition?.batterySpecs[0].voltage
            : 0,
    );
    let current = $state(
        vehicleStore.vehicle?.lastCondition?.batterySpecs[0].current !==
            undefined
            ? vehicleStore.vehicle?.lastCondition?.batterySpecs[0].current
            : 0,
    );

    onMount(() => {
        const socket = io(import.meta.env.VITE_SOCKET_URL);
        socket.connect();

        socket.on("connect-error", () => {
            showErrorToast("Error when connecting to server");
        });
        socket.emit("register-client", vehicleStore.vehicle?.id);
        socket.on("location", (data) => {
            const jsonData = JSON.parse(data) as {
                latitude: number;
                longitude: number;
            };
            if (carIcon) {
                carIcon.setPosition({
                    lat: jsonData.latitude,
                    lng: jsonData.longitude,
                });
            }
            mapComponent?.moveCamera(jsonData.latitude, jsonData.longitude);
        });
        socket.on("metrics", (data) => {
            const jsonData = JSON.parse(data) as {
                speed: number;
                motorTemperature: number;
                batterySpecs: {
                    soc: number;
                    batteryTemperature: number;
                    voltage: number;
                    current: number;
                }[];
            };
            voltage = jsonData.batterySpecs[0].voltage;
            soc = jsonData.batterySpecs[0].soc;
            current = jsonData.batterySpecs[0].current;
            batteryTemperature = jsonData.batterySpecs[0].batteryTemperature;
        });
    });
</script>

<Layout>
    <div class="w-full h-full flex">
        <div class="h-full w-1/3 flex flex-col items-center justify-around">
            <div
                class="w-[95%] h-1/3 flex flex-col justify-center items-center rounded shadow-md bg-gray-100"
            >
                <div class="w-full flex justify-start px-5">
                    <span>{vehicleStore.vehicle?.name}</span>
                </div>
                <img src="/images/car-exp.png" alt="car-logo" class="h-[70%]" />
            </div>

            <div class="w-full h-1/4 flex justify-around">
                <div
                    class="w-1/2 h-full flex flex-col justify-center items-center gap-1"
                >
                    <img src="/images/flash.png" alt="voltage-icon" />
                    <button
                        class="rounded shadow-md w-[90%] h-full bg-gray-100 px-1 py-1 flex justify-center items-center cursor-pointer hover:bg-gray-300"
                        onclick={() => {
                            console.log("clicked");
                        }}
                    >
                        <div class="w-[80%]">
                            <Gauge
                                displayValue={`${voltage} V`}
                                startAngle={75}
                                stopAngle={285}
                                value={voltage}
                                start={0}
                                stop={100}
                                stroke={5}
                                color={"purple"}
                                class="custom-gauge w-full"
                            />
                        </div>
                    </button>
                </div>
                <div
                    class="w-1/2 h-full flex flex-col justify-center items-center gap-1"
                >
                    <img src="/images/flash.png" alt="voltage-icon" />
                    <button
                        class="rounded shadow-md w-[90%] h-full bg-gray-100 px-1 py-1 flex justify-center items-center cursor-pointer hover:bg-gray-300"
                    >
                        <div class="w-[80%]">
                            <Gauge
                                displayValue={`${current} A`}
                                startAngle={75}
                                stopAngle={285}
                                value={current}
                                start={0}
                                stop={200}
                                stroke={5}
                                color={"purple"}
                                class="custom-gauge w-full"
                            />
                        </div>
                    </button>
                </div>
            </div>

            <div class="w-full h-1/4 flex justify-around">
                <div
                    class="w-1/2 h-full flex flex-col justify-center items-center gap-1"
                >
                    <img src="/images/charging.png" alt="soc-icon" />
                    <button
                        class="rounded shadow-md w-[90%] h-full bg-gray-100 px-1 py-1 flex justify-center items-center cursor-pointer hover:bg-gray-300"
                    >
                        <div class="w-[80%]">
                            <Gauge
                                displayValue={`${soc} %`}
                                startAngle={75}
                                stopAngle={285}
                                value={soc}
                                start={0}
                                stop={100}
                                stroke={5}
                                color={"purple"}
                                class="custom-gauge w-full"
                            />
                        </div>
                    </button>
                </div>

                <div
                    class="w-1/2 h-full flex flex-col justify-center items-center gap-1"
                >
                    <img src="/images/thermometer.png" alt="batt-temp-icon" />
                    <button
                        class="rounded shadow-md w-[90%] h-full bg-gray-100 px-1 py-1 flex justify-center items-center cursor-pointer hover:bg-gray-300"
                    >
                        <div class="w-[80%]">
                            <Gauge
                                displayValue={`${batteryTemperature} °C`}
                                startAngle={75}
                                stopAngle={285}
                                value={batteryTemperature}
                                start={0}
                                stop={200}
                                stroke={5}
                                color={"purple"}
                                class="custom-gauge w-full"
                            />
                        </div>
                    </button>
                </div>
            </div>
        </div>
        <Map
            bind:this={mapComponent}
            showVehicles={false}
            showChargingStations={false}
            showBatteries={false}
            loadData={false}
            initialCenter={vehicleStore.vehicle?.lastSeen}
            on:mount={(e) => {
                carIcon = new google.maps.Marker({
                    map: e.detail.map,
                    icon: "/images/car.png",
                });
                if (vehicleStore.vehicle?.lastSeen) {
                    carIcon.setPosition({
                        lat: vehicleStore.vehicle.lastSeen.latitude,
                        lng: vehicleStore.vehicle.lastSeen.longitude,
                    });
                }
            }}
        />
    </div>
</Layout>
