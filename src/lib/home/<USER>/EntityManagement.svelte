<script lang="ts">
    import Layout from "./Layout.svelte";
    import { entityDefinitionStore } from "../../store/entity.store.svelte";
    import type { EntityDefinition } from "../../types/entity.type";
    import Modal from "../../shared/components/Modal.svelte";
    import EntityBuilder from "../../shared/components/EntityBuilder.svelte";
    import EntityMigration from "../../shared/components/EntityMigration.svelte";
    import { showSuccessToast } from "../../shared/utils/toast.util";
    import { push } from "svelte-spa-router";

    let showEntityBuilder = $state(false);
    let showMigration = $state(false);
    let editingEntity: EntityDefinition | undefined = $state();
    let searchQuery = $state('');
    let showDeleteModal = $state(false);
    let entityToDelete: EntityDefinition | undefined = $state();

    // Get filtered entities based on search
    let filteredEntities = $derived(entityDefinitionStore.definitions.filter(entity => {
        if (!searchQuery.trim()) return true;
        const query = searchQuery.toLowerCase();
        return (
            entity.name.toLowerCase().includes(query) ||
            entity.displayName.toLowerCase().includes(query) ||
            entity.description?.toLowerCase().includes(query)
        );
    }));

    // Open entity builder for new entity
    const createEntity = () => {
        editingEntity = undefined;
        showEntityBuilder = true;
    };

    // Open entity builder for editing
    const editEntity = (entity: EntityDefinition) => {
        editingEntity = entity;
        showEntityBuilder = true;
    };

    // Close entity builder
    const closeEntityBuilder = () => {
        showEntityBuilder = false;
        editingEntity = undefined;
    };

    // Confirm delete entity
    const confirmDeleteEntity = (entity: EntityDefinition) => {
        entityToDelete = entity;
        showDeleteModal = true;
    };

    // Delete entity
    const deleteEntity = () => {
        if (entityToDelete) {
            entityDefinitionStore.deleteDefinition(entityToDelete.id);
            showSuccessToast(`Entity "${entityToDelete.displayName}" deleted successfully`);
            showDeleteModal = false;
            entityToDelete = undefined;
        }
    };

    // Navigate to entity instances
    const viewEntityInstances = (entity: EntityDefinition) => {
        push(`/entities/${entity.id}/instances`);
    };

    // Duplicate entity
    const duplicateEntity = (entity: EntityDefinition) => {
        const duplicatedEntity: EntityDefinition = {
            ...entity,
            id: `entity_${Date.now()}`,
            name: `${entity.name}_copy`,
            displayName: `${entity.displayName} (Copy)`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        entityDefinitionStore.addDefinition(duplicatedEntity);
        showSuccessToast(`Entity "${duplicatedEntity.displayName}" created successfully`);
    };

    // Format date for display
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    // Get entity stats
    const getEntityStats = (entity: EntityDefinition) => {
        // This would typically come from the instance store
        // For now, return placeholder data
        return {
            instanceCount: 0,
            fieldCount: entity.fields.length
        };
    };
</script>

<Layout>
    <div class="entity-management">
        <div class="page-header">
            <div class="header-content">
                <h1 class="page-title">Entity Management</h1>
                <p class="page-description">
                    Create and manage custom entities for your application
                </p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick={() => showMigration = true}>
                    Manage Entities
                </button>
                <button class="btn btn-primary" onclick={createEntity}>
                    Create Entity
                </button>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-section">
            <div class="search-bar">
                <input
                    type="text"
                    placeholder="Search entities..."
                    bind:value={searchQuery}
                    class="search-input"
                />
                <div class="search-icon">🔍</div>
            </div>
        </div>

        <!-- Entities Grid -->
        <div class="entities-grid">
            {#each filteredEntities as entity}
                {@const stats = getEntityStats(entity)}
                <div class="entity-card">
                    <div class="entity-header">
                        <div class="entity-info">
                            {#if entity.icon}
                                <div class="entity-icon" style="color: {entity.color}">
                                    {entity.icon}
                                </div>
                            {:else}
                                <div
                                    class="entity-color-badge"
                                    style="background-color: {entity.color}"
                                ></div>
                            {/if}
                            <div class="entity-details">
                                <h3 class="entity-name">{entity.displayName}</h3>
                                <p class="entity-code">{entity.name}</p>
                            </div>
                        </div>
                        <div class="entity-actions">
                            <button
                                class="btn-icon"
                                onclick={() => editEntity(entity)}
                                title="Edit entity"
                            >
                                ✏️
                            </button>
                            <button
                                class="btn-icon"
                                onclick={() => duplicateEntity(entity)}
                                title="Duplicate entity"
                            >
                                📋
                            </button>
                            <button
                                class="btn-icon delete"
                                onclick={() => confirmDeleteEntity(entity)}
                                title="Delete entity"
                            >
                                🗑️
                            </button>
                        </div>
                    </div>

                    {#if entity.description}
                        <p class="entity-description">{entity.description}</p>
                    {/if}

                    <div class="entity-stats">
                        <div class="stat">
                            <span class="stat-value">{stats.fieldCount}</span>
                            <span class="stat-label">Fields</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">{stats.instanceCount}</span>
                            <span class="stat-label">Records</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">{formatDate(entity.createdAt)}</span>
                            <span class="stat-label">Created</span>
                        </div>
                    </div>

                    <div class="entity-footer">
                        <button
                            class="btn btn-secondary btn-sm"
                            onclick={() => viewEntityInstances(entity)}
                        >
                            View Records
                        </button>
                        <button
                            class="btn btn-primary btn-sm"
                            onclick={() => editEntity(entity)}
                        >
                            Configure
                        </button>
                    </div>
                </div>
            {/each}

            {#if filteredEntities.length === 0}
                <div class="empty-state">
                    {#if searchQuery.trim()}
                        <div class="empty-content">
                            <div class="empty-icon">🔍</div>
                            <h3 class="empty-title">No entities found</h3>
                            <p class="empty-description">
                                No entities match your search criteria. Try adjusting your search terms.
                            </p>
                            <button class="btn btn-secondary" onclick={() => searchQuery = ''}>
                                Clear Search
                            </button>
                        </div>
                    {:else}
                        <div class="empty-content">
                            <div class="empty-icon">📋</div>
                            <h3 class="empty-title">No entities yet</h3>
                            <p class="empty-description">
                                Get started by creating your first custom entity. Define the structure and fields that match your business needs.
                            </p>
                            <button class="btn btn-primary" onclick={createEntity}>
                                Create Your First Entity
                            </button>
                        </div>
                    {/if}
                </div>
            {/if}
        </div>
    </div>
</Layout>

<!-- Entity Builder Modal -->
<Modal show={showEntityBuilder} closeModal={closeEntityBuilder}>
    <EntityBuilder
        editingEntity={editingEntity}
        onComplete={closeEntityBuilder}
    />
</Modal>

<!-- Delete Confirmation Modal -->
<Modal show={showDeleteModal} closeModal={() => showDeleteModal = false}>
    <div class="delete-confirmation">
        <div class="delete-header">
            <div class="delete-icon">⚠️</div>
            <h3 class="delete-title">Delete Entity</h3>
        </div>
        <p class="delete-message">
            Are you sure you want to delete the entity "{entityToDelete?.displayName}"?
            This action cannot be undone and will also delete all associated records.
        </p>
        <div class="delete-actions">
            <button
                class="btn btn-secondary"
                onclick={() => showDeleteModal = false}
            >
                Cancel
            </button>
            <button
                class="btn btn-danger"
                onclick={deleteEntity}
            >
                Delete Entity
            </button>
        </div>
    </div>
</Modal>

<!-- Migration Modal -->
<Modal show={showMigration} closeModal={() => showMigration = false}>
    <EntityMigration onComplete={() => showMigration = false} />
</Modal>

<style>
    .entity-management {
        @apply p-6 space-y-6;
    }

    .page-header {
        @apply flex justify-between items-start;
    }

    .header-content {
        @apply space-y-2;
    }

    .header-actions {
        @apply flex space-x-3;
    }

    .page-title {
        @apply text-3xl font-bold text-gray-900;
    }

    .page-description {
        @apply text-gray-600;
    }

    .search-section {
        @apply flex justify-center;
    }

    .search-bar {
        @apply relative w-full max-w-md;
    }

    .search-input {
        @apply w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .search-icon {
        @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400;
    }

    .entities-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
    }

    .entity-card {
        @apply bg-white rounded-lg shadow-md border border-gray-200 p-6 space-y-4 hover:shadow-lg transition-shadow duration-200;
    }

    .entity-header {
        @apply flex justify-between items-start;
    }

    .entity-info {
        @apply flex items-center space-x-3;
    }

    .entity-icon {
        @apply text-2xl;
    }

    .entity-color-badge {
        @apply w-8 h-8 rounded-full;
    }

    .entity-details {
        @apply space-y-1;
    }

    .entity-name {
        @apply text-lg font-semibold text-gray-900;
    }

    .entity-code {
        @apply text-sm text-gray-500 font-mono;
    }

    .entity-actions {
        @apply flex space-x-1;
    }

    .btn-icon {
        @apply p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200;
    }

    .btn-icon.delete {
        @apply hover:text-red-600 hover:bg-red-50;
    }

    .entity-description {
        @apply text-sm text-gray-600;
    }

    .entity-stats {
        @apply flex justify-between items-center py-3 border-t border-gray-100;
    }

    .stat {
        @apply text-center;
    }

    .stat-value {
        @apply block text-lg font-semibold text-gray-900;
    }

    .stat-label {
        @apply text-xs text-gray-500;
    }

    .entity-footer {
        @apply flex justify-between items-center pt-3 border-t border-gray-100;
    }

    .btn {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
    }

    .btn-sm {
        @apply px-3 py-1 text-sm;
    }

    .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700;
    }

    .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400;
    }

    .btn-danger {
        @apply bg-red-600 text-white hover:bg-red-700;
    }

    .empty-state {
        @apply col-span-full flex justify-center items-center min-h-[400px];
    }

    .empty-content {
        @apply text-center space-y-4 max-w-md;
    }

    .empty-icon {
        @apply text-6xl;
    }

    .empty-title {
        @apply text-xl font-semibold text-gray-900;
    }

    .empty-description {
        @apply text-gray-600;
    }

    .delete-confirmation {
        @apply space-y-6 p-6;
    }

    .delete-header {
        @apply flex items-center space-x-3;
    }

    .delete-icon {
        @apply text-2xl;
    }

    .delete-title {
        @apply text-xl font-semibold text-gray-900;
    }

    .delete-message {
        @apply text-gray-600;
    }

    .delete-actions {
        @apply flex justify-end space-x-3;
    }
</style>