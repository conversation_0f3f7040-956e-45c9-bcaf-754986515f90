<script lang="ts">
    import { onMount } from "svelte";
    import Layout from "./Layout.svelte";
    import { entityDefinitionService, entityInstanceService } from "../service/entity.service";
    import type { EntityDefinition, EntityInstance } from "../../types/entity.type";
    import Modal from "../../shared/components/Modal.svelte";
    import DynamicForm from "../../shared/components/DynamicForm.svelte";
    import { showSuccessToast, showErrorToast } from "../../shared/utils/toast.util";
    import { formatFieldValue } from "../../shared/utils/entity.util";
    import { push } from "svelte-spa-router";

    let { params }: { params: { entityId: string } } = $props();

    let entity: EntityDefinition | undefined = $state();
    let instances: EntityInstance[] = $state([]);
    let loading = $state(true);
    let searchQuery = $state('');
    let showCreateModal = $state(false);
    let showEditModal = $state(false);
    let showDeleteModal = $state(false);
    let editingInstance: EntityInstance | undefined = $state();
    let instanceToDelete: EntityInstance | undefined = $state();
    let currentPage = $state(1);
    let pageSize = $state(10);
    let sortField = $state('');
    let sortDirection = $state<'asc' | 'desc'>('asc');

    // Filtered and sorted instances
    let filteredInstances = $derived(() => {
        let result = searchQuery.trim() 
            ? entityInstanceService.search(params.entityId, searchQuery)
            : entityInstanceService.getAll(params.entityId);

        if (sortField) {
            result = entityInstanceService.sort(result, sortField, sortDirection);
        }

        return result;
    });

    // Paginated instances
    let paginatedData = $derived(() => {
        return entityInstanceService.paginate(filteredInstances, currentPage, pageSize);
    });

    // Entity stats
    let stats = $derived(() => {
        return entityInstanceService.getStats(params.entityId);
    });

    // Load entity and instances
    onMount(async () => {
        await loadEntity();
        await loadInstances();
        loading = false;
    });

    const loadEntity = async () => {
        entity = entityDefinitionService.getById(params.entityId);
        if (!entity) {
            showErrorToast('Entity not found');
            push('/entities');
            return;
        }
    };

    const loadInstances = async () => {
        instances = entityInstanceService.getAll(params.entityId);
    };

    // Create new instance
    const createInstance = () => {
        editingInstance = undefined;
        showCreateModal = true;
    };

    // Edit instance
    const editInstance = (instance: EntityInstance) => {
        editingInstance = instance;
        showEditModal = true;
    };

    // Handle form submission
    const handleFormSubmit = (data: Record<string, any>) => {
        if (editingInstance) {
            // Update existing instance
            const result = entityInstanceService.update(params.entityId, editingInstance.id, data);
            if (result.success) {
                showSuccessToast('Record updated successfully');
                loadInstances();
                showEditModal = false;
                editingInstance = undefined;
            } else {
                showErrorToast('Failed to update record');
            }
        } else {
            // Create new instance
            const result = entityInstanceService.create(params.entityId, data);
            if (result.success) {
                showSuccessToast('Record created successfully');
                loadInstances();
                showCreateModal = false;
            } else {
                showErrorToast('Failed to create record');
            }
        }
    };

    // Confirm delete
    const confirmDelete = (instance: EntityInstance) => {
        instanceToDelete = instance;
        showDeleteModal = true;
    };

    // Delete instance
    const deleteInstance = () => {
        if (instanceToDelete) {
            const success = entityInstanceService.delete(params.entityId, instanceToDelete.id);
            if (success) {
                showSuccessToast('Record deleted successfully');
                loadInstances();
            } else {
                showErrorToast('Failed to delete record');
            }
            showDeleteModal = false;
            instanceToDelete = undefined;
        }
    };

    // Handle sorting
    const handleSort = (field: string) => {
        if (sortField === field) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortField = field;
            sortDirection = 'asc';
        }
        currentPage = 1; // Reset to first page when sorting
    };

    // Handle page change
    const changePage = (page: number) => {
        currentPage = page;
    };

    // Export data
    const exportData = (format: 'json' | 'csv') => {
        const data = entityInstanceService.export(params.entityId, format);
        const blob = new Blob([data], { 
            type: format === 'csv' ? 'text/csv' : 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${entity?.name || 'data'}.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    // Get display value for instance
    const getDisplayValue = (instance: EntityInstance): string => {
        if (!entity) return instance.id;
        const displayField = entity.displayField;
        if (displayField && instance.data[displayField]) {
            return String(instance.data[displayField]);
        }
        return instance.id;
    };

    // Format date
    const formatDate = (dateString?: string): string => {
        if (!dateString) return '';
        return new Date(dateString).toLocaleDateString();
    };
</script>

{#if loading}
    <Layout>
        <div class="flex justify-center items-center h-64">
            <div class="text-lg">Loading...</div>
        </div>
    </Layout>
{:else if entity}
    <Layout>
        <div class="entity-instances">
            <!-- Header -->
            <div class="page-header">
                <div class="header-content">
                    <div class="breadcrumb">
                        <button onclick={() => push('/entities')} class="breadcrumb-link">
                            Entities
                        </button>
                        <span class="breadcrumb-separator">›</span>
                        <span class="breadcrumb-current">{entity.displayName}</span>
                    </div>
                    <h1 class="page-title">{entity.displayName} Records</h1>
                    {#if entity.description}
                        <p class="page-description">{entity.description}</p>
                    {/if}
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick={() => exportData('csv')}>
                        Export CSV
                    </button>
                    <button class="btn btn-secondary" onclick={() => exportData('json')}>
                        Export JSON
                    </button>
                    <button class="btn btn-primary" onclick={createInstance}>
                        Add Record
                    </button>
                </div>
            </div>

            <!-- Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{stats.total}</div>
                    <div class="stat-label">Total Records</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{stats.active}</div>
                    <div class="stat-label">Active</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{stats.createdToday}</div>
                    <div class="stat-label">Created Today</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{stats.createdThisWeek}</div>
                    <div class="stat-label">This Week</div>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="search-section">
                <div class="search-bar">
                    <input
                        type="text"
                        placeholder="Search records..."
                        bind:value={searchQuery}
                        class="search-input"
                    />
                    <div class="search-icon">🔍</div>
                </div>
                <div class="page-size-selector">
                    <label>Show:</label>
                    <select bind:value={pageSize} class="page-size-select">
                        <option value={10}>10</option>
                        <option value={25}>25</option>
                        <option value={50}>50</option>
                        <option value={100}>100</option>
                    </select>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            {#each entity.fields.slice(0, 5) as field}
                                <th>
                                    <button 
                                        class="sort-button"
                                        onclick={() => handleSort(field.name)}
                                    >
                                        {field.label}
                                        {#if sortField === field.name}
                                            <span class="sort-indicator">
                                                {sortDirection === 'asc' ? '↑' : '↓'}
                                            </span>
                                        {/if}
                                    </button>
                                </th>
                            {/each}
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each paginatedData.items as instance}
                            <tr>
                                {#each entity.fields.slice(0, 5) as field}
                                    <td>
                                        {formatFieldValue(field, instance.data[field.name])}
                                    </td>
                                {/each}
                                <td>{formatDate(instance.createdAt)}</td>
                                <td>
                                    <div class="action-buttons">
                                        <button
                                            class="btn-icon"
                                            onclick={() => editInstance(instance)}
                                            title="Edit"
                                        >
                                            ✏️
                                        </button>
                                        <button
                                            class="btn-icon delete"
                                            onclick={() => confirmDelete(instance)}
                                            title="Delete"
                                        >
                                            🗑️
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>

                {#if paginatedData.items.length === 0}
                    <div class="empty-state">
                        {#if searchQuery.trim()}
                            <div class="empty-content">
                                <div class="empty-icon">🔍</div>
                                <h3 class="empty-title">No records found</h3>
                                <p class="empty-description">
                                    No records match your search criteria.
                                </p>
                                <button class="btn btn-secondary" onclick={() => searchQuery = ''}>
                                    Clear Search
                                </button>
                            </div>
                        {:else}
                            <div class="empty-content">
                                <div class="empty-icon">📄</div>
                                <h3 class="empty-title">No records yet</h3>
                                <p class="empty-description">
                                    Get started by creating your first record.
                                </p>
                                <button class="btn btn-primary" onclick={createInstance}>
                                    Add First Record
                                </button>
                            </div>
                        {/if}
                    </div>
                {/if}
            </div>

            <!-- Pagination -->
            {#if paginatedData.totalPages > 1}
                <div class="pagination">
                    <button
                        class="pagination-btn"
                        onclick={() => changePage(currentPage - 1)}
                        disabled={!paginatedData.hasPrevious}
                    >
                        Previous
                    </button>
                    
                    <div class="pagination-info">
                        Page {paginatedData.currentPage} of {paginatedData.totalPages}
                        ({paginatedData.totalItems} total records)
                    </div>
                    
                    <button
                        class="pagination-btn"
                        onclick={() => changePage(currentPage + 1)}
                        disabled={!paginatedData.hasNext}
                    >
                        Next
                    </button>
                </div>
            {/if}
        </div>
    </Layout>
{/if}

<!-- Create Modal -->
<Modal show={showCreateModal} closeModal={() => showCreateModal = false}>
    {#if entity}
        <DynamicForm
            definition={entity}
            onSubmit={handleFormSubmit}
            onCancel={() => showCreateModal = false}
            submitLabel="Create Record"
        />
    {/if}
</Modal>

<!-- Edit Modal -->
<Modal show={showEditModal} closeModal={() => showEditModal = false}>
    {#if entity && editingInstance}
        <DynamicForm
            definition={entity}
            initialData={editingInstance.data}
            onSubmit={handleFormSubmit}
            onCancel={() => showEditModal = false}
            submitLabel="Update Record"
        />
    {/if}
</Modal>

<!-- Delete Confirmation Modal -->
<Modal show={showDeleteModal} closeModal={() => showDeleteModal = false}>
    <div class="delete-confirmation">
        <div class="delete-header">
            <div class="delete-icon">⚠️</div>
            <h3 class="delete-title">Delete Record</h3>
        </div>
        <p class="delete-message">
            Are you sure you want to delete this record? This action cannot be undone.
        </p>
        <div class="delete-actions">
            <button
                class="btn btn-secondary"
                onclick={() => showDeleteModal = false}
            >
                Cancel
            </button>
            <button
                class="btn btn-danger"
                onclick={deleteInstance}
            >
                Delete Record
            </button>
        </div>
    </div>
</Modal>

<style>
    .entity-instances {
        @apply p-6 space-y-6;
    }

    .page-header {
        @apply flex justify-between items-start;
    }

    .header-content {
        @apply space-y-2;
    }

    .breadcrumb {
        @apply flex items-center space-x-2 text-sm text-gray-600;
    }

    .breadcrumb-link {
        @apply hover:text-blue-600 cursor-pointer;
    }

    .breadcrumb-separator {
        @apply text-gray-400;
    }

    .breadcrumb-current {
        @apply font-medium text-gray-900;
    }

    .page-title {
        @apply text-3xl font-bold text-gray-900;
    }

    .page-description {
        @apply text-gray-600;
    }

    .header-actions {
        @apply flex space-x-3;
    }

    .stats-grid {
        @apply grid grid-cols-1 md:grid-cols-4 gap-4;
    }

    .stat-card {
        @apply bg-white rounded-lg shadow p-6 text-center;
    }

    .stat-value {
        @apply text-3xl font-bold text-gray-900;
    }

    .stat-label {
        @apply text-sm text-gray-600 mt-1;
    }

    .search-section {
        @apply flex justify-between items-center;
    }

    .search-bar {
        @apply relative flex-1 max-w-md;
    }

    .search-input {
        @apply w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .search-icon {
        @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400;
    }

    .page-size-selector {
        @apply flex items-center space-x-2 text-sm;
    }

    .page-size-select {
        @apply border border-gray-300 rounded px-2 py-1;
    }

    .table-container {
        @apply bg-white rounded-lg shadow overflow-hidden;
    }

    .data-table {
        @apply w-full;
    }

    .data-table th {
        @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .data-table td {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-t border-gray-200;
    }

    .sort-button {
        @apply flex items-center space-x-1 hover:text-gray-700;
    }

    .sort-indicator {
        @apply text-blue-600;
    }

    .action-buttons {
        @apply flex space-x-2;
    }

    .btn-icon {
        @apply p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded;
    }

    .btn-icon.delete {
        @apply hover:text-red-600 hover:bg-red-50;
    }

    .empty-state {
        @apply flex justify-center items-center py-12;
    }

    .empty-content {
        @apply text-center space-y-4 max-w-md;
    }

    .empty-icon {
        @apply text-6xl;
    }

    .empty-title {
        @apply text-xl font-semibold text-gray-900;
    }

    .empty-description {
        @apply text-gray-600;
    }

    .pagination {
        @apply flex justify-between items-center;
    }

    .pagination-btn {
        @apply px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .pagination-info {
        @apply text-sm text-gray-600;
    }

    .btn {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
    }

    .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700;
    }

    .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400;
    }

    .btn-danger {
        @apply bg-red-600 text-white hover:bg-red-700;
    }

    .delete-confirmation {
        @apply space-y-6 p-6;
    }

    .delete-header {
        @apply flex items-center space-x-3;
    }

    .delete-icon {
        @apply text-2xl;
    }

    .delete-title {
        @apply text-xl font-semibold text-gray-900;
    }

    .delete-message {
        @apply text-gray-600;
    }

    .delete-actions {
        @apply flex justify-end space-x-3;
    }
</style>
