<script lang="ts">
    import { onMount } from "svelte";
    import { getDriverTrips } from "../service/trip.service";
    import { tripStore } from "../../store/trip.svelte";
    import Table from "../../shared/components/Table.svelte";
    import Loader from "../../shared/components/Loader.svelte";

    const { driverId } = $props();
    let loading = $state(false);

    onMount(() => {
        loading = true;
        getDriverTrips(
            driverId,
            (response) => {
                tripStore.trips = response.filter(
                    (trip) => trip.driverId === driverId,
                );
            },
            () => (loading = false),
        );
    });
    const formatTripData = () => {
        return tripStore.trips.map((trip) => ({
            client: trip.client,
            destination: trip.destinationAddress,
            description: trip.description,
            dueDate: trip.dueDate,
            status: trip.status,
        }));
    };
</script>

<div>
    {#if loading}
        <Loader />
    {:else}
        <Table
            columns={[
                "client",
                "destination",
                "description",
                "dueDate",
                "status",
            ]}
            data={formatTripData()}
            rowClick={(trip) => {
                console.log(trip);
            }}
        />
    {/if}
</div>
