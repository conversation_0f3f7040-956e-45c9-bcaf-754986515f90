<script lang="ts">
    import { onMount } from "svelte";
    import Layout from "./Layout.svelte";
    import { initMap } from "../../shared/utils/map.util";
    import { fleetStore } from "../../store/vehicle.svelte";
    import { chargingStationStore } from "../../store/chargingStation.svelte";
    import { getChargingStations, getDrivers, getFleet } from "../service/fleet.service";
    import SearchBar from "./components/SearchBar.svelte";
    import type { Vehicle } from "../../types/vehicle.type";
    import { driversStore } from "../../store/driver.store.svelte";
    import Modal from "../../shared/components/Modal.svelte";
    import Loader from "../../shared/components/Loader.svelte";
    import Map from "../../shared/components/Map.svelte";

    let map:
        | {
              map: google.maps.Map;
              center: google.maps.LatLngLiteral | google.maps.LatLng;
          }
        | undefined = $state();
    let loading = $state(true);

    onMount(() => {
        getFleet((response) => {
            fleetStore.fleet = response.items;
        });
        loading = false;
    });

    const moveMapCamera = (vehicle: Vehicle | null) => {
        if (!map || !vehicle?.lastSeen) {
            console.warn("Map or vehicle.lastSeen is not available");
            return;
        }
        map.map.setCenter({
            lat: vehicle.lastSeen.latitude,
            lng: vehicle.lastSeen.longitude,
        });
        map.map.setZoom(14);
    };

    function handleMapMount(event: CustomEvent<{ map: google.maps.Map }>) {
        map = {
            map: event.detail.map,
            center: event.detail.map.getCenter()!,
        };
    }
</script>

<Layout>
    <div
        class="absolute z-10 shadow-md w-[50%] h-10 bg-slate-50 rounded-md top-[10vh]"
    >
        <SearchBar {moveMapCamera} />
    </div>
    <Map
        showVehicles={true}
        showChargingStations={true}
        showBatteries={true}
        loadData={true}
        on:mount={handleMapMount}
    />
    <div
        class="absolute bottom-4 left-4 z-20 bg-white rounded-md shadow-md p-4 flex flex-col gap-2"
    >
        <div class="flex items-center gap-2">
            <img src="/images/car.png" alt="Vehicle Icon" class="w-6 h-6" />
            <span class="text-sm font-medium text-gray-700">Vehicle</span>
        </div>
        <div class="flex items-center gap-2">
            <img
                src="/images/charging-station.png"
                alt="Charging Station Icon"
                class="w-6 h-6"
            />
            <span class="text-sm font-medium text-gray-700"
                >Charging Station</span
            >
        </div>
        <div class="flex items-center gap-2">
            <img
                src="/images/accumulator.png"
                alt="Battery Icon"
                class="w-6 h-6"
            />
            <span class="text-sm font-medium text-gray-700">Battery</span>
        </div>
    </div>
</Layout>

<Modal closeModal={() => {}} show={loading}>
    <div class="w-full h-full flex justify-center items-center">
        <Loader />
    </div>
</Modal>