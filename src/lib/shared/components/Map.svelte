<script lang="ts">
    import { onMount, createEventDispatcher } from "svelte";
    import { initMap } from "../utils/map.util";
    import type { Vehicle } from "../../types/vehicle.type";
    import type { ChargingStation } from "../../types/chargingStation.type";
    import type { Battery } from "../../types/battery.type";
    import { fleetStore } from "../../store/vehicle.svelte";
    import { chargingStationStore } from "../../store/chargingStation.svelte";
    import { batteryStore } from "../../store/battery.svelte";
    import { 
        getFleet, 
        getChargingStations 
    } from "../../home/<USER>/fleet.service";
    import { getAvailableBatteries } from "../../home/<USER>/battery.service";

    // Create a dispatcher for events
    const dispatch = createEventDispatcher<{
        mount: { map: google.maps.Map };
    }>();

    let { 
        showVehicles = true, 
        showChargingStations = true,
        showBatteries = false,
        initialCenter = undefined,
        loadData = true,
        onMarkerClick = null
    } = $props<{
        showVehicles?: boolean;
        showChargingStations?: boolean;
        showBatteries?: boolean;
        initialCenter?: { latitude: number; longitude: number };
        loadData?: boolean;
        onMarkerClick?: (item: Vehicle | ChargingStation | Battery, type: 'vehicle' | 'station' | 'battery') => void;
    }>();

    let mapDiv: HTMLDivElement;
    let map: { 
        map: google.maps.Map; 
        center: google.maps.LatLngLiteral | google.maps.LatLng 
    } | undefined = $state();
    
    let markers: google.maps.Marker[] = [];
    let loading = $state(false);

    onMount(async () => {
        loading = true;
        map = await initMap(mapDiv, initialCenter);
        
        // Dispatch the mount event with the map instance
        dispatch('mount', { map: map!.map });
        
        if (loadData) {
            loadMapData();
        } else {
            addMarkers();
        }
        
        loading = false;
    });

    function loadMapData() {
        if (showVehicles) {
            getFleet((response) => {
                fleetStore.fleet = response.items;
                addVehicleMarkers(map!.map, "/images/car.png");
            });
        }

        if (showChargingStations) {
            getChargingStations((response) => {
                chargingStationStore.chargingStation = response.items;
                addStationMarkers(map!.map, "/images/charging-station.png");
            });
        }

        if (showBatteries) {
            getAvailableBatteries((response) => {
                batteryStore.batteries = response.items;
                addBatteryMarkers(map!.map, "/images/accumulator.png");
            });
        }
    }

    $effect(() => {
        if (map && !loadData) {
            clearMarkers();
            addMarkers();
        }
    });

    function clearMarkers() {
        markers.forEach(marker => marker.setMap(null));
        markers = [];
    }

    function addMarkers() {
        if (!map) return;
        
        if (showVehicles && fleetStore.fleet.length > 0) {
            addVehicleMarkers(map.map, "/images/car.png");
        }
        
        if (showChargingStations && chargingStationStore.chargingStation.length > 0) {
            addStationMarkers(map.map, "/images/charging-station.png");
        }
        
        if (showBatteries && batteryStore.batteries.length > 0) {
            addBatteryMarkers(map.map, "/images/battery.png");
        }
    }

    function addVehicleMarkers(map: google.maps.Map, icon: string) {
        fleetStore.fleet
            .filter((vehicle) => vehicle.lastSeen !== null)
            .forEach((vehicle) => {
                const marker = new google.maps.Marker({
                    position: {
                        lat: vehicle.lastSeen.latitude,
                        lng: vehicle.lastSeen.longitude,
                    },
                    map: map,
                    icon: icon,
                });
                
                if (onMarkerClick) {
                    marker.addListener("click", () => {
                        onMarkerClick(vehicle, 'vehicle');
                    });
                }
                
                markers.push(marker);
            });
    }

    function addStationMarkers(map: google.maps.Map, icon: string) {
        chargingStationStore.chargingStation.forEach((station) => {
            const marker = new google.maps.Marker({
                position: {
                    lat: station.address.latitude,
                    lng: station.address.longitude,
                },
                map: map,
                icon: icon,
            });
            
            if (onMarkerClick) {
                marker.addListener("click", () => {
                    onMarkerClick(station, 'station');
                });
            }
            
            markers.push(marker);
        });
    }

    function addBatteryMarkers(map: google.maps.Map, icon: string) {
        batteryStore.batteries.forEach((battery) => {
            const marker = new google.maps.Marker({
                position: {
                    lat: battery.location.latitude,
                    lng: battery.location.longitude,
                },
                map: map,
                icon: icon,
            });
            
            if (onMarkerClick) {
                marker.addListener("click", () => {
                    onMarkerClick(battery, 'battery');
                });
            }
            
            markers.push(marker);
        });
    }

    export function moveCamera(latitude: number, longitude: number, zoom: number = 14) {
        if (map) {
            map.map.setCenter({ lat: latitude, lng: longitude });
            map.map.setZoom(zoom);
        }
    }
</script>

<div bind:this={mapDiv} class="w-full h-full"></div>