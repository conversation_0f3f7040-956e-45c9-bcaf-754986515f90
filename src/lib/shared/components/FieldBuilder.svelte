<script lang="ts">
    import type { EntityField, FieldType, ValidationRule, FieldOption } from "../../types/entity.type";
    import Input from "./Input.svelte";

    let {
        field,
        onSave,
        onCancel
    }: {
        field?: EntityField;
        onSave: (field: EntityField) => void;
        onCancel: () => void;
    } = $props();

    // Field types with their display names
    const fieldTypes: { value: FieldType; label: string }[] = [
        { value: 'text', label: 'Text' },
        { value: 'textarea', label: 'Textarea' },
        { value: 'number', label: 'Number' },
        { value: 'email', label: 'Email' },
        { value: 'password', label: 'Password' },
        { value: 'date', label: 'Date' },
        { value: 'datetime', label: 'Date & Time' },
        { value: 'time', label: 'Time' },
        { value: 'boolean', label: 'Boolean (Yes/No)' },
        { value: 'select', label: 'Select (Dropdown)' },
        { value: 'multiselect', label: 'Multi-Select' },
        { value: 'radio', label: 'Radio Buttons' },
        { value: 'checkbox', label: 'Checkboxes' },
        { value: 'file', label: 'File Upload' },
        { value: 'url', label: 'URL' },
        { value: 'phone', label: 'Phone Number' },
        { value: 'color', label: 'Color Picker' },
        { value: 'range', label: 'Range Slider' },
        { value: 'location', label: 'Location (Lat/Lng)' },
        { value: 'relationship', label: 'Relationship' },
        { value: 'calculated', label: 'Calculated Field' }
    ];

    // Initialize field data
    let fieldData = $state({
        id: field?.id || `field_${Date.now()}`,
        name: field?.name || '',
        label: field?.label || '',
        type: field?.type || 'text' as FieldType,
        required: field?.required || false,
        defaultValue: field?.defaultValue || '',
        placeholder: field?.placeholder || '',
        helpText: field?.helpText || '',
        multiple: field?.multiple || false,
        min: field?.min,
        max: field?.max,
        step: field?.step,
        accept: field?.accept || '',
        rows: field?.rows || 3,
        cols: field?.cols,
        order: field?.order || 0,
        // Relationship fields
        relationshipType: field?.relationshipType || 'many-to-one',
        relatedEntity: field?.relatedEntity || '',
        relatedField: field?.relatedField || '',
        cascadeDelete: field?.cascadeDelete || false,
        // Calculated fields
        calculationFormula: field?.calculationFormula || '',
        calculationDependenciesStr: field?.calculationDependencies?.join(', ') || '',
        recalculateOnChange: field?.recalculateOnChange || true
    });

    let options = $state<FieldOption[]>(field?.options || []);
    let validationRules = $state<ValidationRule[]>(field?.validation || []);
    let errors = $state<Record<string, string>>({});

    // Field types that support options
    const optionSupportedTypes = ['select', 'multiselect', 'radio', 'checkbox'];

    // Field types that support min/max
    const minMaxSupportedTypes = ['number', 'range', 'date', 'datetime', 'time'];

    // Field types that support multiple
    const multipleSupportedTypes = ['select', 'file'];

    // Validation rule types
    const validationTypes = [
        { value: 'required', label: 'Required' },
        { value: 'minLength', label: 'Minimum Length' },
        { value: 'maxLength', label: 'Maximum Length' },
        { value: 'min', label: 'Minimum Value' },
        { value: 'max', label: 'Maximum Value' },
        { value: 'pattern', label: 'Pattern (Regex)' },
        { value: 'email', label: 'Email Format' },
        { value: 'url', label: 'URL Format' }
    ];

    // Generate field name from label
    const generateFieldName = () => {
        if (fieldData.label && !fieldData.name) {
            fieldData.name = fieldData.label
                .toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+|_+$/g, '');
        }
    };

    // Add new option
    const addOption = () => {
        options = [...options, { value: '', label: '', disabled: false }];
    };

    // Remove option
    const removeOption = (index: number) => {
        options = options.filter((_, i) => i !== index);
    };

    // Add validation rule
    const addValidationRule = () => {
        validationRules = [...validationRules, { type: 'required', message: '', value: undefined }];
    };

    // Remove validation rule
    const removeValidationRule = (index: number) => {
        validationRules = validationRules.filter((_, i) => i !== index);
    };

    // Validate field data
    const validateField = (): boolean => {
        errors = {};

        if (!fieldData.name.trim()) {
            errors.name = 'Field name is required';
        } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(fieldData.name)) {
            errors.name = 'Field name must start with a letter and contain only letters, numbers, and underscores';
        }

        if (!fieldData.label.trim()) {
            errors.label = 'Field label is required';
        }

        if (optionSupportedTypes.includes(fieldData.type) && options.length === 0) {
            errors.options = 'At least one option is required for this field type';
        }

        // Validate options
        if (optionSupportedTypes.includes(fieldData.type)) {
            options.forEach((option, index) => {
                if (!option.value.toString().trim()) {
                    errors[`option_value_${index}`] = 'Option value is required';
                }
                if (!option.label.trim()) {
                    errors[`option_label_${index}`] = 'Option label is required';
                }
            });
        }

        // Validate validation rules
        validationRules.forEach((rule, index) => {
            if (!rule.message.trim()) {
                errors[`rule_message_${index}`] = 'Validation message is required';
            }
            if (['minLength', 'maxLength', 'min', 'max', 'pattern'].includes(rule.type) && !rule.value) {
                errors[`rule_value_${index}`] = 'Validation value is required';
            }
        });

        return Object.keys(errors).length === 0;
    };

    // Save field
    const saveField = () => {
        if (!validateField()) return;

        const newField: EntityField = {
            id: fieldData.id,
            name: fieldData.name,
            label: fieldData.label,
            type: fieldData.type,
            required: fieldData.required,
            defaultValue: fieldData.defaultValue || undefined,
            placeholder: fieldData.placeholder || undefined,
            helpText: fieldData.helpText || undefined,
            validation: validationRules,
            options: optionSupportedTypes.includes(fieldData.type) ? options : undefined,
            multiple: multipleSupportedTypes.includes(fieldData.type) ? fieldData.multiple : undefined,
            min: minMaxSupportedTypes.includes(fieldData.type) ? fieldData.min : undefined,
            max: minMaxSupportedTypes.includes(fieldData.type) ? fieldData.max : undefined,
            step: fieldData.type === 'number' || fieldData.type === 'range' ? fieldData.step : undefined,
            accept: fieldData.type === 'file' ? fieldData.accept : undefined,
            rows: fieldData.type === 'textarea' ? fieldData.rows : undefined,
            cols: fieldData.type === 'textarea' ? fieldData.cols : undefined,
            // Relationship properties
            relationshipType: fieldData.type === 'relationship' ? fieldData.relationshipType : undefined,
            relatedEntity: fieldData.type === 'relationship' ? fieldData.relatedEntity : undefined,
            relatedField: fieldData.type === 'relationship' ? fieldData.relatedField : undefined,
            cascadeDelete: fieldData.type === 'relationship' ? fieldData.cascadeDelete : undefined,
            // Calculated field properties
            calculationFormula: fieldData.type === 'calculated' ? fieldData.calculationFormula : undefined,
            calculationDependencies: fieldData.type === 'calculated' && fieldData.calculationDependenciesStr
                ? fieldData.calculationDependenciesStr.split(',').map(s => s.trim()).filter(s => s)
                : undefined,
            recalculateOnChange: fieldData.type === 'calculated' ? fieldData.recalculateOnChange : undefined,
            order: fieldData.order
        };

        onSave(newField);
    };
</script>

<div class="field-builder">
    <div class="builder-header">
        <h3 class="builder-title">
            {field ? 'Edit Field' : 'Add New Field'}
        </h3>
    </div>

    <div class="builder-content">
        <!-- Basic Information -->
        <div class="section">
            <h4 class="section-title">Basic Information</h4>
            <div class="form-grid">
                <div class="form-field">
                    <Input
                        type="text"
                        placeholder="Field Label"
                        bind:value={fieldData.label}
                        errorMessage={errors.label}
                        onChange={generateFieldName}
                    />
                    <label class="field-label">Label</label>
                </div>

                <div class="form-field">
                    <Input
                        type="text"
                        placeholder="field_name"
                        bind:value={fieldData.name}
                        errorMessage={errors.name}
                    />
                    <label class="field-label">Field Name</label>
                </div>

                <div class="form-field">
                    <select bind:value={fieldData.type} class="select-input">
                        {#each fieldTypes as type}
                            <option value={type.value}>{type.label}</option>
                        {/each}
                    </select>
                    <label class="field-label">Field Type</label>
                </div>

                <div class="form-field">
                    <label class="checkbox-field">
                        <input type="checkbox" bind:checked={fieldData.required} />
                        <span>Required Field</span>
                    </label>
                </div>

                <div class="form-field full-width">
                    <Input
                        type="text"
                        placeholder="Placeholder text"
                        bind:value={fieldData.placeholder}
                    />
                    <label class="field-label">Placeholder</label>
                </div>

                <div class="form-field full-width">
                    <textarea
                        placeholder="Help text (optional)"
                        bind:value={fieldData.helpText}
                        class="textarea-input"
                        rows="2"
                    ></textarea>
                    <label class="field-label">Help Text</label>
                </div>
            </div>
        </div>

        <!-- Type-specific options -->
        {#if minMaxSupportedTypes.includes(fieldData.type)}
            <div class="section">
                <h4 class="section-title">Value Constraints</h4>
                <div class="form-grid">
                    <div class="form-field">
                        <Input
                            type="number"
                            placeholder="Minimum value"
                            bind:value={fieldData.min}
                        />
                        <label class="field-label">Minimum</label>
                    </div>

                    <div class="form-field">
                        <Input
                            type="number"
                            placeholder="Maximum value"
                            bind:value={fieldData.max}
                        />
                        <label class="field-label">Maximum</label>
                    </div>

                    {#if fieldData.type === 'number' || fieldData.type === 'range'}
                        <div class="form-field">
                            <Input
                                type="number"
                                placeholder="Step value"
                                bind:value={fieldData.step}
                            />
                            <label class="field-label">Step</label>
                        </div>
                    {/if}
                </div>
            </div>
        {/if}

        {#if fieldData.type === 'textarea'}
            <div class="section">
                <h4 class="section-title">Textarea Options</h4>
                <div class="form-grid">
                    <div class="form-field">
                        <Input
                            type="number"
                            placeholder="Number of rows"
                            bind:value={fieldData.rows}
                        />
                        <label class="field-label">Rows</label>
                    </div>

                    <div class="form-field">
                        <Input
                            type="number"
                            placeholder="Number of columns"
                            bind:value={fieldData.cols}
                        />
                        <label class="field-label">Columns</label>
                    </div>
                </div>
            </div>
        {/if}

        {#if fieldData.type === 'file'}
            <div class="section">
                <h4 class="section-title">File Options</h4>
                <div class="form-grid">
                    <div class="form-field">
                        <Input
                            type="text"
                            placeholder="e.g., .jpg,.png,.pdf"
                            bind:value={fieldData.accept}
                        />
                        <label class="field-label">Accepted File Types</label>
                    </div>

                    <div class="form-field">
                        <label class="checkbox-field">
                            <input type="checkbox" bind:checked={fieldData.multiple} />
                            <span>Allow Multiple Files</span>
                        </label>
                    </div>
                </div>
            </div>
        {/if}

        {#if multipleSupportedTypes.includes(fieldData.type) && fieldData.type === 'select'}
            <div class="section">
                <h4 class="section-title">Select Options</h4>
                <div class="form-field">
                    <label class="checkbox-field">
                        <input type="checkbox" bind:checked={fieldData.multiple} />
                        <span>Allow Multiple Selections</span>
                    </label>
                </div>
            </div>
        {/if}

        <!-- Options for select, radio, checkbox fields -->
        {#if optionSupportedTypes.includes(fieldData.type)}
            <div class="section">
                <div class="section-header">
                    <h4 class="section-title">Options</h4>
                    <button class="btn btn-sm btn-primary" onclick={addOption}>
                        Add Option
                    </button>
                </div>

                {#if errors.options}
                    <p class="error-message">{errors.options}</p>
                {/if}

                <div class="options-list">
                    {#each options as option, index}
                        <div class="option-item">
                            <div class="option-inputs">
                                <Input
                                    type="text"
                                    placeholder="Option value"
                                    bind:value={option.value}
                                    errorMessage={errors[`option_value_${index}`]}
                                />
                                <Input
                                    type="text"
                                    placeholder="Option label"
                                    bind:value={option.label}
                                    errorMessage={errors[`option_label_${index}`]}
                                />
                                <label class="checkbox-field">
                                    <input type="checkbox" bind:checked={option.disabled} />
                                    <span>Disabled</span>
                                </label>
                            </div>
                            <button
                                class="btn-icon delete"
                                onclick={() => removeOption(index)}
                                title="Remove option"
                            >
                                🗑️
                            </button>
                        </div>
                    {/each}

                    {#if options.length === 0}
                        <div class="empty-state">
                            <p>No options defined. Click "Add Option" to get started.</p>
                        </div>
                    {/if}
                </div>
            </div>
        {/if}

        <!-- Relationship Configuration -->
        {#if fieldData.type === 'relationship'}
            <div class="section">
                <h4 class="section-title">Relationship Configuration</h4>
                <div class="form-grid">
                    <div class="form-field">
                        <select bind:value={fieldData.relationshipType} class="select-input">
                            <option value="many-to-one">Many to One</option>
                            <option value="one-to-many">One to Many</option>
                            <option value="one-to-one">One to One</option>
                            <option value="many-to-many">Many to Many</option>
                        </select>
                        <label class="field-label">Relationship Type</label>
                    </div>
                    <div class="form-field">
                        <Input
                            type="text"
                            placeholder="Related entity name"
                            bind:value={fieldData.relatedEntity}
                        />
                        <label class="field-label">Related Entity</label>
                    </div>
                    <div class="form-field">
                        <Input
                            type="text"
                            placeholder="Related field name"
                            bind:value={fieldData.relatedField}
                        />
                        <label class="field-label">Related Field</label>
                    </div>
                    <div class="form-field">
                        <label class="checkbox-field">
                            <input type="checkbox" bind:checked={fieldData.cascadeDelete} />
                            <span>Cascade Delete</span>
                        </label>
                    </div>
                </div>
            </div>
        {/if}

        <!-- Calculated Field Configuration -->
        {#if fieldData.type === 'calculated'}
            <div class="section">
                <h4 class="section-title">Calculation Configuration</h4>
                <div class="form-grid">
                    <div class="form-field full-width">
                        <textarea
                            placeholder="JavaScript expression (e.g., field1 + field2 * 0.1)"
                            bind:value={fieldData.calculationFormula}
                            class="textarea-input"
                            rows="3"
                        ></textarea>
                        <label class="field-label">Calculation Formula</label>
                    </div>
                    <div class="form-field full-width">
                        <Input
                            type="text"
                            placeholder="field1, field2, field3 (comma-separated)"
                            bind:value={fieldData.calculationDependenciesStr}
                        />
                        <label class="field-label">Dependent Fields</label>
                    </div>
                    <div class="form-field">
                        <label class="checkbox-field">
                            <input type="checkbox" bind:checked={fieldData.recalculateOnChange} />
                            <span>Recalculate on Change</span>
                        </label>
                    </div>
                </div>
            </div>
        {/if}

        <!-- Validation Rules -->
        <div class="section">
            <div class="section-header">
                <h4 class="section-title">Validation Rules</h4>
                <button class="btn btn-sm btn-primary" onclick={addValidationRule}>
                    Add Rule
                </button>
            </div>

            <div class="validation-list">
                {#each validationRules as rule, index}
                    <div class="validation-item">
                        <div class="validation-inputs">
                            <select bind:value={rule.type} class="select-input">
                                {#each validationTypes as type}
                                    <option value={type.value}>{type.label}</option>
                                {/each}
                            </select>

                            {#if ['minLength', 'maxLength', 'min', 'max', 'pattern'].includes(rule.type)}
                                <Input
                                    type={rule.type === 'pattern' ? 'text' : 'number'}
                                    placeholder={rule.type === 'pattern' ? 'Regular expression' : 'Value'}
                                    bind:value={rule.value}
                                    errorMessage={errors[`rule_value_${index}`]}
                                />
                            {/if}

                            <Input
                                type="text"
                                placeholder="Error message"
                                bind:value={rule.message}
                                errorMessage={errors[`rule_message_${index}`]}
                            />
                        </div>
                        <button
                            class="btn-icon delete"
                            onclick={() => removeValidationRule(index)}
                            title="Remove rule"
                        >
                            🗑️
                        </button>
                    </div>
                {/each}

                {#if validationRules.length === 0}
                    <div class="empty-state">
                        <p>No validation rules defined.</p>
                    </div>
                {/if}
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="builder-actions">
        <button class="btn btn-secondary" onclick={onCancel}>
            Cancel
        </button>
        <button class="btn btn-primary" onclick={saveField}>
            {field ? 'Update Field' : 'Add Field'}
        </button>
    </div>
</div>

<style>
    .field-builder {
        @apply max-w-2xl mx-auto bg-white rounded-lg p-6 space-y-6 max-h-[80vh] overflow-y-auto;
    }

    .builder-header {
        @apply text-center;
    }

    .builder-title {
        @apply text-xl font-bold text-gray-900;
    }

    .section {
        @apply space-y-4;
    }

    .section-title {
        @apply text-lg font-semibold text-gray-800;
    }

    .section-header {
        @apply flex justify-between items-center;
    }

    .form-grid {
        @apply grid grid-cols-1 md:grid-cols-2 gap-4;
    }

    .form-field {
        @apply space-y-1;
    }

    .form-field.full-width {
        @apply md:col-span-2;
    }

    .field-label {
        @apply text-sm font-medium text-gray-700;
    }

    .textarea-input, .select-input {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .checkbox-field {
        @apply flex items-center space-x-2 cursor-pointer;
    }

    .checkbox-field span {
        @apply text-sm text-gray-700;
    }

    .options-list, .validation-list {
        @apply space-y-3;
    }

    .option-item, .validation-item {
        @apply flex items-start space-x-3 p-3 border border-gray-200 rounded-lg;
    }

    .option-inputs, .validation-inputs {
        @apply flex-1 grid grid-cols-1 md:grid-cols-3 gap-2;
    }

    .btn-icon {
        @apply p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded;
    }

    .empty-state {
        @apply text-center py-4 text-gray-500 text-sm;
    }

    .builder-actions {
        @apply flex justify-end space-x-3 pt-6 border-t border-gray-200;
    }

    .btn {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
    }

    .btn-sm {
        @apply px-3 py-1 text-sm;
    }

    .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700;
    }

    .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400;
    }

    .error-message {
        @apply text-sm text-red-600;
    }
</style>
