<script lang="ts">
    import type { EntityDefinition, EntityField, FormSection } from "../../types/entity.type";
    import type { ValidationResult } from "../../types/validation.type";
    import { 
        validateEntityData, 
        initializeFormData, 
        getVisibleFields, 
        sortFieldsByOrder 
    } from "../utils/entity.util";
    import DynamicField from "./DynamicField.svelte";
    import Loader from "./Loader.svelte";

    let {
        definition,
        initialData = {},
        onSubmit,
        onCancel,
        submitLabel = "Save",
        cancelLabel = "Cancel",
        loading = false,
        readonly = false
    }: {
        definition: EntityDefinition;
        initialData?: Record<string, any>;
        onSubmit: (data: Record<string, any>) => void;
        onCancel?: () => void;
        submitLabel?: string;
        cancelLabel?: string;
        loading?: boolean;
        readonly?: boolean;
    } = $props();

    // Initialize form data
    let formData = $state({ ...initializeFormData(definition), ...initialData });
    let validationResult = $state<ValidationResult>();
    let isSubmitting = $state(false);

    // Get visible fields based on current form data
    $derived visibleFields = getVisibleFields(sortFieldsByOrder(definition.fields), formData);

    // Handle form submission
    const handleSubmit = (event: Event) => {
        event.preventDefault();
        event.stopPropagation();

        if (readonly) return;

        isSubmitting = true;
        const validation = validateEntityData(definition, formData);
        
        if (validation.success) {
            validationResult = undefined;
            onSubmit(formData);
        } else {
            validationResult = {
                success: false,
                errors: Object.fromEntries(
                    validation.error.errors.map(error => [error.path[0], error.message])
                )
            };
        }
        
        isSubmitting = false;
    };

    // Handle field value change
    const handleFieldChange = (fieldName: string, value: any) => {
        formData[fieldName] = value;
        // Clear validation error for this field
        if (validationResult?.errors[fieldName]) {
            validationResult.errors[fieldName] = '';
        }
    };

    // Group fields by sections if form layout has sections
    $derived fieldSections = (() => {
        // For now, we'll create a single section with all fields
        // This can be enhanced to support multiple sections from form layout
        return [{
            id: 'default',
            title: definition.displayName,
            fields: visibleFields.map(f => f.id)
        }];
    })();
</script>

<form class="dynamic-form" onsubmit={handleSubmit}>
    <div class="form-header">
        <h2 class="form-title">{definition.displayName}</h2>
        {#if definition.description}
            <p class="form-description">{definition.description}</p>
        {/if}
    </div>

    <div class="form-content">
        {#each fieldSections as section}
            <div class="form-section">
                {#if section.title && fieldSections.length > 1}
                    <h3 class="section-title">{section.title}</h3>
                {/if}
                
                <div class="fields-grid">
                    {#each visibleFields.filter(f => section.fields.includes(f.id)) as field}
                        <div class="field-container">
                            <DynamicField
                                {field}
                                bind:value={formData[field.name]}
                                errorMessage={validationResult?.errors[field.name]}
                                {readonly}
                                onChange={(value) => handleFieldChange(field.name, value)}
                            />
                        </div>
                    {/each}
                </div>
            </div>
        {/each}
    </div>

    {#if !readonly}
        <div class="form-actions">
            {#if onCancel}
                <button 
                    type="button" 
                    class="btn btn-secondary"
                    onclick={onCancel}
                    disabled={isSubmitting || loading}
                >
                    {cancelLabel}
                </button>
            {/if}
            
            <button 
                type="submit" 
                class="btn btn-primary"
                disabled={isSubmitting || loading}
            >
                {#if isSubmitting || loading}
                    <Loader size="small" />
                {/if}
                {submitLabel}
            </button>
        </div>
    {/if}
</form>

<style>
    .dynamic-form {
        @apply w-full max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6;
    }

    .form-header {
        @apply mb-6;
    }

    .form-title {
        @apply text-2xl font-bold text-gray-900 mb-2;
    }

    .form-description {
        @apply text-gray-600 text-sm;
    }

    .form-content {
        @apply space-y-6;
    }

    .form-section {
        @apply space-y-4;
    }

    .section-title {
        @apply text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2;
    }

    .fields-grid {
        @apply grid grid-cols-1 md:grid-cols-2 gap-4;
    }

    .field-container {
        @apply space-y-1;
    }

    /* Full width fields */
    .field-container :global(.field-textarea),
    .field-container :global(.field-location),
    .field-container :global(.field-file) {
        @apply md:col-span-2;
    }

    .form-actions {
        @apply flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6;
    }

    .btn {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200 flex items-center space-x-2;
    }

    .btn:disabled {
        @apply opacity-50 cursor-not-allowed;
    }

    .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
    }

    .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
    }
</style>
