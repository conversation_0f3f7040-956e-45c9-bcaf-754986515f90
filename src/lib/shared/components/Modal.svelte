<script lang="ts">
  let {
    show = false,
    closeModal,
    children,
  } = $props<{
    show: boolean;
    closeModal: () => void;
    children: () => any;
  }>();

  const handleClickOutside = (event: MouseEvent) => {
    if ((event.target as HTMLElement).id === "back-drop") {
      closeModal();
    }
  };
</script>

{#if show}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    id="back-drop"
    class="modal-overlay fixed inset-0 flex items-center justify-center backdrop-brightness-50 bg-opacity-50 z-50"
    onclick={handleClickOutside}
  >
    <div
      class="modal-container bg-secondary p-6 rounded-lg shadow-lg min-w-2xl w-fit h-fit max-h-fit relative"
    >
      <button class="absolute top-2 right-2 text-white cursor-pointer" onclick={closeModal}>
        &#10005;
      </button>
      {@render children()}
    </div>
  </div>
{/if}
