<script lang="ts">
    import type { EntityDefinition, EntityField, FieldType, ValidationRule } from "../../types/entity.type";
    import { entityDefinitionStore } from "../../store/entity.store.svelte";
    import { showSuccessToast, showErrorToast } from "../utils/toast.util";
    import Input from "./Input.svelte";
    import Modal from "./Modal.svelte";
    import FieldBuilder from "./FieldBuilder.svelte";

    let {
        onComplete,
        editingEntity
    }: {
        onComplete: () => void;
        editingEntity?: EntityDefinition;
    } = $props();

    // Entity definition state
    let entityData = $state({
        name: editingEntity?.name || '',
        displayName: editingEntity?.displayName || '',
        description: editingEntity?.description || '',
        icon: editingEntity?.icon || '',
        color: editingEntity?.color || '#3B82F6',
        timestamps: editingEntity?.timestamps ?? true,
        softDelete: editingEntity?.softDelete ?? false,
        searchableFields: editingEntity?.searchableFields || [],
        displayField: editingEntity?.displayField || ''
    });

    let fields = $state<EntityField[]>(editingEntity?.fields || []);
    let showFieldBuilder = $state(false);
    let editingField: EntityField | undefined = $state();
    let errors = $state<Record<string, string>>({});

    // Validate entity data
    const validateEntity = (): boolean => {
        errors = {};
        
        if (!entityData.name.trim()) {
            errors.name = 'Entity name is required';
        } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(entityData.name)) {
            errors.name = 'Entity name must start with a letter and contain only letters, numbers, and underscores';
        }

        if (!entityData.displayName.trim()) {
            errors.displayName = 'Display name is required';
        }

        if (fields.length === 0) {
            errors.fields = 'At least one field is required';
        }

        if (entityData.displayField && !fields.find(f => f.name === entityData.displayField)) {
            errors.displayField = 'Display field must be one of the defined fields';
        }

        return Object.keys(errors).length === 0;
    };

    // Save entity definition
    const saveEntity = () => {
        if (!validateEntity()) {
            showErrorToast('Please fix the validation errors');
            return;
        }

        const definition: EntityDefinition = {
            id: editingEntity?.id || `entity_${Date.now()}`,
            name: entityData.name,
            displayName: entityData.displayName,
            description: entityData.description,
            icon: entityData.icon,
            color: entityData.color,
            fields: fields,
            timestamps: entityData.timestamps,
            softDelete: entityData.softDelete,
            searchableFields: entityData.searchableFields,
            displayField: entityData.displayField || fields[0]?.name || '',
            createdAt: editingEntity?.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: editingEntity?.createdBy || 'current-user' // TODO: Get from auth
        };

        if (editingEntity) {
            entityDefinitionStore.updateDefinition(editingEntity.id, definition);
            showSuccessToast('Entity updated successfully');
        } else {
            entityDefinitionStore.addDefinition(definition);
            showSuccessToast('Entity created successfully');
        }

        onComplete();
    };

    // Add new field
    const addField = () => {
        editingField = undefined;
        showFieldBuilder = true;
    };

    // Edit existing field
    const editField = (field: EntityField) => {
        editingField = field;
        showFieldBuilder = true;
    };

    // Handle field save from FieldBuilder
    const handleFieldSave = (field: EntityField) => {
        if (editingField) {
            // Update existing field
            fields = fields.map(f => f.id === editingField!.id ? field : f);
        } else {
            // Add new field
            fields = [...fields, field];
        }
        
        showFieldBuilder = false;
        editingField = undefined;
    };

    // Delete field
    const deleteField = (fieldId: string) => {
        fields = fields.filter(f => f.id !== fieldId);
        
        // Remove from searchable fields if present
        entityData.searchableFields = entityData.searchableFields.filter(f => f !== fieldId);
        
        // Clear display field if it was the deleted field
        if (entityData.displayField === fieldId) {
            entityData.displayField = '';
        }
    };

    // Move field up/down
    const moveField = (fieldId: string, direction: 'up' | 'down') => {
        const index = fields.findIndex(f => f.id === fieldId);
        if (index === -1) return;

        const newIndex = direction === 'up' ? index - 1 : index + 1;
        if (newIndex < 0 || newIndex >= fields.length) return;

        const newFields = [...fields];
        [newFields[index], newFields[newIndex]] = [newFields[newIndex], newFields[index]];
        
        // Update order values
        newFields.forEach((field, idx) => {
            field.order = idx;
        });
        
        fields = newFields;
    };

    // Toggle field searchability
    const toggleSearchable = (fieldName: string) => {
        if (entityData.searchableFields.includes(fieldName)) {
            entityData.searchableFields = entityData.searchableFields.filter(f => f !== fieldName);
        } else {
            entityData.searchableFields = [...entityData.searchableFields, fieldName];
        }
    };
</script>

<div class="entity-builder">
    <div class="builder-header">
        <h2 class="builder-title">
            {editingEntity ? 'Edit Entity' : 'Create New Entity'}
        </h2>
        <p class="builder-description">
            Define the structure and properties of your custom entity
        </p>
    </div>

    <div class="builder-content">
        <!-- Basic Information -->
        <div class="section">
            <h3 class="section-title">Basic Information</h3>
            <div class="form-grid">
                <div class="form-field">
                    <Input
                        type="text"
                        placeholder="Entity Name (e.g., customer, product)"
                        bind:value={entityData.name}
                        errorMessage={errors.name}
                    />
                    <label class="field-label">Entity Name</label>
                </div>

                <div class="form-field">
                    <Input
                        type="text"
                        placeholder="Display Name (e.g., Customer, Product)"
                        bind:value={entityData.displayName}
                        errorMessage={errors.displayName}
                    />
                    <label class="field-label">Display Name</label>
                </div>

                <div class="form-field full-width">
                    <textarea
                        placeholder="Description (optional)"
                        bind:value={entityData.description}
                        class="textarea-input"
                        rows="3"
                    ></textarea>
                    <label class="field-label">Description</label>
                </div>

                <div class="form-field">
                    <Input
                        type="text"
                        placeholder="Icon class (optional)"
                        bind:value={entityData.icon}
                    />
                    <label class="field-label">Icon</label>
                </div>

                <div class="form-field">
                    <Input
                        type="color"
                        placeholder="Entity color"
                        bind:value={entityData.color}
                    />
                    <label class="field-label">Color</label>
                </div>
            </div>
        </div>

        <!-- Options -->
        <div class="section">
            <h3 class="section-title">Options</h3>
            <div class="options-grid">
                <label class="checkbox-field">
                    <input type="checkbox" bind:checked={entityData.timestamps} />
                    <span>Include timestamps (createdAt, updatedAt)</span>
                </label>

                <label class="checkbox-field">
                    <input type="checkbox" bind:checked={entityData.softDelete} />
                    <span>Enable soft delete</span>
                </label>
            </div>
        </div>

        <!-- Fields -->
        <div class="section">
            <div class="section-header">
                <h3 class="section-title">Fields</h3>
                <button class="btn btn-primary" onclick={addField}>
                    Add Field
                </button>
            </div>

            {#if errors.fields}
                <p class="error-message">{errors.fields}</p>
            {/if}

            <div class="fields-list">
                {#each fields as field, index}
                    <div class="field-item">
                        <div class="field-info">
                            <div class="field-header">
                                <span class="field-name">{field.label}</span>
                                <span class="field-type">{field.type}</span>
                                {#if field.required}
                                    <span class="required-badge">Required</span>
                                {/if}
                            </div>
                            {#if field.helpText}
                                <p class="field-help">{field.helpText}</p>
                            {/if}
                        </div>

                        <div class="field-actions">
                            <label class="searchable-toggle">
                                <input
                                    type="checkbox"
                                    checked={entityData.searchableFields.includes(field.name)}
                                    onchange={() => toggleSearchable(field.name)}
                                />
                                <span>Searchable</span>
                            </label>

                            <button
                                class="btn-icon"
                                onclick={() => moveField(field.id, 'up')}
                                disabled={index === 0}
                                title="Move up"
                            >
                                ↑
                            </button>

                            <button
                                class="btn-icon"
                                onclick={() => moveField(field.id, 'down')}
                                disabled={index === fields.length - 1}
                                title="Move down"
                            >
                                ↓
                            </button>

                            <button
                                class="btn-icon edit"
                                onclick={() => editField(field)}
                                title="Edit field"
                            >
                                ✏️
                            </button>

                            <button
                                class="btn-icon delete"
                                onclick={() => deleteField(field.id)}
                                title="Delete field"
                            >
                                🗑️
                            </button>
                        </div>
                    </div>
                {/each}

                {#if fields.length === 0}
                    <div class="empty-state">
                        <p>No fields defined yet. Click "Add Field" to get started.</p>
                    </div>
                {/if}
            </div>
        </div>

        <!-- Display Field Selection -->
        {#if fields.length > 0}
            <div class="section">
                <h3 class="section-title">Display Settings</h3>
                <div class="form-field">
                    <select bind:value={entityData.displayField} class="select-input">
                        <option value="">-- Select primary display field --</option>
                        {#each fields as field}
                            <option value={field.name}>{field.label}</option>
                        {/each}
                    </select>
                    <label class="field-label">Primary Display Field</label>
                    {#if errors.displayField}
                        <p class="error-message">{errors.displayField}</p>
                    {/if}
                </div>
            </div>
        {/if}
    </div>

    <!-- Actions -->
    <div class="builder-actions">
        <button class="btn btn-secondary" onclick={onComplete}>
            Cancel
        </button>
        <button class="btn btn-primary" onclick={saveEntity}>
            {editingEntity ? 'Update Entity' : 'Create Entity'}
        </button>
    </div>
</div>

<!-- Field Builder Modal -->
<Modal show={showFieldBuilder} closeModal={() => showFieldBuilder = false}>
    <FieldBuilder
        field={editingField}
        onSave={handleFieldSave}
        onCancel={() => showFieldBuilder = false}
    />
</Modal>

<style>
    .entity-builder {
        @apply max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6 space-y-6;
    }

    .builder-header {
        @apply text-center space-y-2;
    }

    .builder-title {
        @apply text-2xl font-bold text-gray-900;
    }

    .builder-description {
        @apply text-gray-600;
    }

    .section {
        @apply space-y-4;
    }

    .section-title {
        @apply text-lg font-semibold text-gray-800;
    }

    .section-header {
        @apply flex justify-between items-center;
    }

    .form-grid {
        @apply grid grid-cols-1 md:grid-cols-2 gap-4;
    }

    .form-field {
        @apply space-y-1;
    }

    .form-field.full-width {
        @apply md:col-span-2;
    }

    .field-label {
        @apply text-sm font-medium text-gray-700;
    }

    .textarea-input, .select-input {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .options-grid {
        @apply space-y-3;
    }

    .checkbox-field {
        @apply flex items-center space-x-2 cursor-pointer;
    }

    .checkbox-field span {
        @apply text-sm text-gray-700;
    }

    .fields-list {
        @apply space-y-3;
    }

    .field-item {
        @apply flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50;
    }

    .field-info {
        @apply flex-1 space-y-1;
    }

    .field-header {
        @apply flex items-center space-x-3;
    }

    .field-name {
        @apply font-medium text-gray-900;
    }

    .field-type {
        @apply text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded;
    }

    .required-badge {
        @apply text-xs text-red-600 bg-red-100 px-2 py-1 rounded;
    }

    .field-help {
        @apply text-sm text-gray-600;
    }

    .field-actions {
        @apply flex items-center space-x-2;
    }

    .searchable-toggle {
        @apply flex items-center space-x-1 text-sm cursor-pointer;
    }

    .btn-icon {
        @apply p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded;
    }

    .btn-icon:disabled {
        @apply opacity-50 cursor-not-allowed;
    }

    .btn-icon.edit {
        @apply hover:text-blue-600;
    }

    .btn-icon.delete {
        @apply hover:text-red-600;
    }

    .empty-state {
        @apply text-center py-8 text-gray-500;
    }

    .builder-actions {
        @apply flex justify-end space-x-3 pt-6 border-t border-gray-200;
    }

    .btn {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
    }

    .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700;
    }

    .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400;
    }

    .error-message {
        @apply text-sm text-red-600;
    }
</style>
