<script lang="ts" generics="T">
    import { camelCaseToTitleCase } from "../utils/miscellaneous.util";

    let {
        columns,
        data,
        actions = [],
        rowClick,
    }: {
        columns: string[];
        data: T[];
        actions?: { iconClass: string; onClick: (row: T) => void }[];
        rowClick?: (row: T) => void;
    } = $props();

    function getNestedValue(obj: any, path: string) {
        return path.split('.').reduce((prev, curr) => 
            prev && prev[curr] ? prev[curr] : '', obj);
    }
</script>

<table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            {#each columns as title}
                <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                    {camelCaseToTitleCase(
                        title.toString().split('.').pop()?.replace("-", " ").replace("_", " ") || '',
                    )}
                </th>
            {/each}
            {#if actions?.length}
                <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                    Actions
                </th>
            {/if}
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {#each data as element}
            <tr
                class="hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
            >
                {#each columns as path}
                    <td
                        class="px-6 py-4 whitespace-nowrap"
                        onclick={() => rowClick?.(element)}
                    >
                        {getNestedValue(element, path)}
                    </td>
                {/each}
                {#if actions.length}
                    <td class="px-6 py-4 whitespace-nowrap flex space-x-2">
                        {#each actions as action}
                            <button
                                aria-label="action-btn"
                                class="p-1 text-gray-900 hover:text-primary cursor-pointer"
                                onclick={() => action.onClick(element)}
                            >
                                <i class={action.iconClass + " text-lg"}></i>
                            </button>
                        {/each}
                    </td>
                {/if}
            </tr>
        {/each}
    </tbody>
</table>
