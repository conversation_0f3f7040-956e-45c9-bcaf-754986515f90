<script lang="ts">
    import type { EntityField } from "../../types/entity.type";
    import Input from "./Input.svelte";

    let {
        field,
        value = $bindable(),
        errorMessage,
        readonly = false,
        onChange
    }: {
        field: EntityField;
        value?: any;
        errorMessage?: string;
        readonly?: boolean;
        onChange?: (value: any) => void;
    } = $props();

    // Handle value changes
    const handleChange = (newValue: any) => {
        value = newValue;
        onChange?.(newValue);
    };

    // Get input type for HTML input element
    const getInputType = (fieldType: string): string => {
        switch (fieldType) {
            case 'email': return 'email';
            case 'password': return 'password';
            case 'number': return 'number';
            case 'date': return 'date';
            case 'datetime': return 'datetime-local';
            case 'time': return 'time';
            case 'url': return 'url';
            case 'phone': return 'tel';
            case 'color': return 'color';
            case 'range': return 'range';
            default: return 'text';
        }
    };
</script>

<div class="dynamic-field field-{field.type}" class:readonly>
    <label for={field.id} class="field-label">
        {field.label}
        {#if field.required}
            <span class="required-indicator">*</span>
        {/if}
    </label>

    {#if field.helpText}
        <p class="field-help">{field.helpText}</p>
    {/if}

    <div class="field-input">
        {#if field.type === 'textarea'}
            <textarea
                id={field.id}
                bind:value
                placeholder={field.placeholder}
                rows={field.rows || 3}
                cols={field.cols}
                {readonly}
                class="textarea-input"
                class:error={errorMessage}
                onchange={() => handleChange(value)}
            ></textarea>

        {:else if field.type === 'select'}
            <select
                id={field.id}
                bind:value
                {readonly}
                class="select-input"
                class:error={errorMessage}
                onchange={() => handleChange(value)}
            >
                {#if !field.required}
                    <option value="">-- Select an option --</option>
                {/if}
                {#each field.options || [] as option}
                    <option value={option.value} disabled={option.disabled}>
                        {option.label}
                    </option>
                {/each}
            </select>

        {:else if field.type === 'multiselect'}
            <select
                id={field.id}
                bind:value
                multiple
                {readonly}
                class="select-input multiselect"
                class:error={errorMessage}
                onchange={() => handleChange(value)}
            >
                {#each field.options || [] as option}
                    <option value={option.value} disabled={option.disabled}>
                        {option.label}
                    </option>
                {/each}
            </select>

        {:else if field.type === 'radio'}
            <div class="radio-group">
                {#each field.options || [] as option}
                    <label class="radio-option">
                        <input
                            type="radio"
                            name={field.id}
                            value={option.value}
                            bind:group={value}
                            disabled={readonly || option.disabled}
                            onchange={() => handleChange(value)}
                        />
                        <span class="radio-label">{option.label}</span>
                    </label>
                {/each}
            </div>

        {:else if field.type === 'checkbox'}
            <div class="checkbox-group">
                {#each field.options || [] as option}
                    <label class="checkbox-option">
                        <input
                            type="checkbox"
                            value={option.value}
                            checked={Array.isArray(value) && value.includes(option.value)}
                            disabled={readonly || option.disabled}
                            onchange={(e) => {
                                const checked = e.target?.checked;
                                let newValue = Array.isArray(value) ? [...value] : [];
                                if (checked) {
                                    if (!newValue.includes(option.value)) {
                                        newValue.push(option.value);
                                    }
                                } else {
                                    newValue = newValue.filter(v => v !== option.value);
                                }
                                handleChange(newValue);
                            }}
                        />
                        <span class="checkbox-label">{option.label}</span>
                    </label>
                {/each}
            </div>

        {:else if field.type === 'boolean'}
            <label class="boolean-field">
                <input
                    type="checkbox"
                    id={field.id}
                    bind:checked={value}
                    {readonly}
                    onchange={() => handleChange(value)}
                />
                <span class="boolean-label">{field.placeholder || 'Yes'}</span>
            </label>

        {:else if field.type === 'file'}
            <div class="file-upload-container">
                <input
                    type="file"
                    id={field.id}
                    multiple={field.multiple}
                    accept={field.accept}
                    {readonly}
                    class="file-input-hidden"
                    class:error={errorMessage}
                    onchange={(e) => {
                        const files = e.target?.files;
                        if (files) {
                            const fileArray = Array.from(files);
                            const fileNames = fileArray.map(f => f.name);
                            handleChange(field.multiple ? fileNames : fileNames[0] || '');

                            // Store file objects for display
                            fileValue = files;
                        }
                    }}
                />
                <label for={field.id} class="file-upload-label" class:readonly>
                    <div class="file-upload-content">
                        <div class="file-upload-icon">📁</div>
                        <div class="file-upload-text">
                            {#if value && (Array.isArray(value) ? value.length > 0 : value)}
                                {Array.isArray(value) ? `${value.length} file${value.length > 1 ? 's' : ''} selected` : value}
                            {:else}
                                Click to select {field.multiple ? 'files' : 'file'}
                            {/if}
                        </div>
                        {#if field.accept}
                            <div class="file-upload-hint">
                                Accepted: {field.accept}
                            </div>
                        {/if}
                    </div>
                </label>
                {#if value && (Array.isArray(value) ? value.length > 0 : value)}
                    <div class="file-list">
                        {#each Array.isArray(value) ? value : [value] as fileName, index}
                            <div class="file-item">
                                <div class="file-info">
                                    <span class="file-name">{fileName}</span>
                                </div>
                                {#if !readonly}
                                    <button
                                        type="button"
                                        class="file-remove"
                                        onclick={() => {
                                            if (Array.isArray(value)) {
                                                const newValue = value.filter((_, i) => i !== index);
                                                handleChange(newValue.length > 0 ? newValue : '');
                                            } else {
                                                handleChange('');
                                            }
                                        }}
                                    >
                                        ✕
                                    </button>
                                {/if}
                            </div>
                        {/each}
                    </div>
                {/if}
            </div>

        {:else if field.type === 'range'}
            <div class="range-field">
                <input
                    type="range"
                    id={field.id}
                    bind:value
                    min={field.min}
                    max={field.max}
                    step={field.step || 1}
                    {readonly}
                    class="range-input"
                    oninput={() => handleChange(value)}
                />
                <span class="range-value">{value}</span>
            </div>

        {:else if field.type === 'location'}
            <div class="location-field">
                <div class="location-inputs">
                    <input
                        type="number"
                        placeholder="Latitude"
                        bind:value={value.latitude}
                        {readonly}
                        class="location-input"
                        step="any"
                        onchange={() => handleChange(value)}
                    />
                    <input
                        type="number"
                        placeholder="Longitude"
                        bind:value={value.longitude}
                        {readonly}
                        class="location-input"
                        step="any"
                        onchange={() => handleChange(value)}
                    />
                </div>
                <input
                    type="text"
                    placeholder="Address (optional)"
                    bind:value={value.address}
                    {readonly}
                    class="location-address"
                    onchange={() => handleChange(value)}
                />
            </div>

        {:else if field.type === 'relationship'}
            <div class="relationship-field">
                {#if field.relatedEntity}
                    <select
                        id={field.id}
                        bind:value
                        {readonly}
                        class="select-input"
                        class:error={errorMessage}
                        onchange={() => handleChange(value)}
                    >
                        <option value="">Select {field.label}</option>
                        <!-- TODO: Load related entity instances -->
                        <option value="placeholder">Related Item (Placeholder)</option>
                    </select>
                    <div class="relationship-info">
                        <span class="relationship-type">{field.relationshipType || 'many-to-one'}</span>
                        <span class="related-entity">→ {field.relatedEntity}</span>
                    </div>
                {:else}
                    <div class="relationship-error">
                        Relationship configuration incomplete
                    </div>
                {/if}
            </div>

        {:else if field.type === 'calculated'}
            <div class="calculated-field">
                <input
                    type="text"
                    id={field.id}
                    bind:value
                    readonly
                    class="calculated-input"
                    placeholder="Calculated value will appear here"
                />
                {#if field.calculationFormula}
                    <div class="calculation-info">
                        <span class="calculation-formula">Formula: {field.calculationFormula}</span>
                        {#if field.calculationDependencies && field.calculationDependencies.length > 0}
                            <span class="calculation-deps">
                                Depends on: {field.calculationDependencies.join(', ')}
                            </span>
                        {/if}
                    </div>
                {/if}
            </div>

        {:else}
            <!-- Default input for text, email, password, number, date, etc. -->
            <Input
                type={getInputType(field.type)}
                bind:value
                placeholder={field.placeholder || ''}
                {errorMessage}
                readonly={readonly}
                min={field.min}
                max={field.max}
                step={field.step}
                onChange={() => handleChange(value)}
            />
        {/if}
    </div>

    {#if errorMessage}
        <p class="field-error">{errorMessage}</p>
    {/if}
</div>

<style>
    .dynamic-field {
        @apply space-y-2;
    }

    .field-label {
        @apply block text-sm font-medium text-gray-700;
    }

    .required-indicator {
        @apply text-red-500 ml-1;
    }

    .field-help {
        @apply text-xs text-gray-500;
    }

    .field-error {
        @apply text-sm text-red-600;
    }

    .textarea-input, .select-input {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .file-upload-container {
        @apply space-y-2;
    }

    .file-input-hidden {
        @apply sr-only;
    }

    .file-upload-label {
        @apply block w-full p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors duration-200;
    }

    .file-upload-label.readonly {
        @apply cursor-not-allowed opacity-50;
    }

    .file-upload-content {
        @apply text-center space-y-2;
    }

    .file-upload-icon {
        @apply text-2xl;
    }

    .file-upload-text {
        @apply text-sm font-medium text-gray-700;
    }

    .file-upload-hint {
        @apply text-xs text-gray-500;
    }

    .file-list {
        @apply space-y-2;
    }

    .file-item {
        @apply flex items-center justify-between p-2 bg-gray-50 rounded border;
    }

    .file-info {
        @apply flex-1;
    }

    .file-name {
        @apply text-sm font-medium text-gray-900;
    }

    .file-remove {
        @apply ml-2 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded;
    }

    .textarea-input {
        @apply resize-vertical;
    }

    .multiselect {
        @apply min-h-[100px];
    }

    .error {
        @apply border-red-500 focus:ring-red-500 focus:border-red-500;
    }

    .radio-group, .checkbox-group {
        @apply space-y-2;
    }

    .radio-option, .checkbox-option {
        @apply flex items-center space-x-2 cursor-pointer;
    }

    .radio-label, .checkbox-label {
        @apply text-sm text-gray-700;
    }

    .boolean-field {
        @apply flex items-center space-x-2 cursor-pointer;
    }

    .boolean-label {
        @apply text-sm text-gray-700;
    }

    .range-field {
        @apply flex items-center space-x-3;
    }

    .range-input {
        @apply flex-1;
    }

    .range-value {
        @apply text-sm font-medium text-gray-700 min-w-[3rem] text-center;
    }

    .location-field {
        @apply space-y-2;
    }

    .location-inputs {
        @apply grid grid-cols-2 gap-2;
    }

    .location-input, .location-address {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }

    .relationship-field {
        @apply space-y-2;
    }

    .relationship-info {
        @apply flex items-center space-x-2 text-xs text-gray-500;
    }

    .relationship-type {
        @apply px-2 py-1 bg-blue-100 text-blue-800 rounded;
    }

    .related-entity {
        @apply font-medium;
    }

    .relationship-error {
        @apply text-sm text-red-600 p-2 bg-red-50 border border-red-200 rounded;
    }

    .calculated-field {
        @apply space-y-2;
    }

    .calculated-input {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700;
    }

    .calculation-info {
        @apply space-y-1 text-xs text-gray-500;
    }

    .calculation-formula {
        @apply block font-mono bg-gray-100 px-2 py-1 rounded;
    }

    .calculation-deps {
        @apply block;
    }

    .readonly {
        @apply opacity-75;
    }

    .readonly input, .readonly textarea, .readonly select {
        @apply bg-gray-100 cursor-not-allowed;
    }
</style>
