<script lang="ts">
    import { migrationUtils, initializeSampleEntities } from "../utils/migration.util";
    import { showSuccessToast, showErrorToast } from "../utils/toast.util";
    import { entityDefinitionService } from "../../home/<USER>/entity.service";

    let {
        onComplete
    }: {
        onComplete: () => void;
    } = $props();

    let activeTab = $state<'samples' | 'import' | 'export'>('samples');
    let importData = $state('');
    let importing = $state(false);
    let exporting = $state(false);

    // Check if sample entities already exist
    let samplesExist = $derived(migrationUtils.sampleEntitiesExist());
    let entityCount = $derived(entityDefinitionService.getAll().length);

    // Create sample entities
    const createSamples = () => {
        try {
            migrationUtils.createSampleEntities();
            showSuccessToast('Sample entities created successfully!');
            onComplete();
        } catch (error) {
            showErrorToast('Failed to create sample entities');
            console.error(error);
        }
    };

    // Import entities
    const importEntities = async () => {
        if (!importData.trim()) {
            showErrorToast('Please provide JSON data to import');
            return;
        }

        importing = true;
        try {
            const result = migrationUtils.importEntityDefinitions(importData);
            if (result.success) {
                showSuccessToast(`Successfully imported ${result.imported} entities`);
                if (result.errors.length > 0) {
                    console.warn('Import warnings:', result.errors);
                }
                importData = '';
                onComplete();
            } else {
                showErrorToast('Failed to import entities');
                console.error(result.errors);
            }
        } catch (error) {
            showErrorToast('Import failed');
            console.error(error);
        } finally {
            importing = false;
        }
    };

    // Export entities
    const exportEntities = () => {
        exporting = true;
        try {
            const data = migrationUtils.exportEntityDefinitions();
            
            // Download as file
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `entity-definitions-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showSuccessToast('Entity definitions exported successfully');
        } catch (error) {
            showErrorToast('Export failed');
            console.error(error);
        } finally {
            exporting = false;
        }
    };

    // Clear all entities
    const clearAllEntities = () => {
        if (confirm('Are you sure you want to delete all entity definitions? This action cannot be undone.')) {
            try {
                migrationUtils.clearAllEntities();
                showSuccessToast('All entities cleared');
                onComplete();
            } catch (error) {
                showErrorToast('Failed to clear entities');
                console.error(error);
            }
        }
    };
</script>

<div class="entity-migration">
    <div class="migration-header">
        <h2 class="migration-title">Entity Management</h2>
        <p class="migration-description">
            Set up your dynamic entities by creating samples, importing existing definitions, or exporting your current setup.
        </p>
    </div>

    <!-- Tabs -->
    <div class="tabs">
        <button 
            class="tab"
            class:active={activeTab === 'samples'}
            onclick={() => activeTab = 'samples'}
        >
            Sample Entities
        </button>
        <button 
            class="tab"
            class:active={activeTab === 'import'}
            onclick={() => activeTab = 'import'}
        >
            Import
        </button>
        <button 
            class="tab"
            class:active={activeTab === 'export'}
            onclick={() => activeTab = 'export'}
        >
            Export
        </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        {#if activeTab === 'samples'}
            <div class="samples-tab">
                <div class="info-card">
                    <div class="info-icon">📋</div>
                    <div class="info-content">
                        <h3 class="info-title">Sample Entities</h3>
                        <p class="info-description">
                            Get started quickly with pre-built entity templates including Customer, Product, and Service Request entities.
                        </p>
                        <div class="info-stats">
                            <div class="stat">
                                <span class="stat-value">{entityCount}</span>
                                <span class="stat-label">Current Entities</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">3</span>
                                <span class="stat-label">Sample Templates</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="sample-entities">
                    <h4 class="section-title">Available Sample Entities:</h4>
                    <div class="entity-list">
                        <div class="entity-preview">
                            <div class="entity-icon">👤</div>
                            <div class="entity-info">
                                <h5 class="entity-name">Customer</h5>
                                <p class="entity-desc">Customer information and contact details</p>
                                <div class="entity-fields">6 fields including name, email, phone, company</div>
                            </div>
                        </div>
                        <div class="entity-preview">
                            <div class="entity-icon">📦</div>
                            <div class="entity-info">
                                <h5 class="entity-name">Product</h5>
                                <p class="entity-desc">Product catalog and inventory management</p>
                                <div class="entity-fields">7 fields including name, SKU, price, category</div>
                            </div>
                        </div>
                        <div class="entity-preview">
                            <div class="entity-icon">🔧</div>
                            <div class="entity-info">
                                <h5 class="entity-name">Service Request</h5>
                                <p class="entity-desc">Customer service requests and support tickets</p>
                                <div class="entity-fields">7 fields including title, priority, status</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="actions">
                    {#if samplesExist}
                        <div class="warning-message">
                            <span class="warning-icon">⚠️</span>
                            Sample entities already exist. Creating samples again may create duplicates.
                        </div>
                    {/if}
                    <button 
                        class="btn btn-primary"
                        onclick={createSamples}
                        disabled={importing || exporting}
                    >
                        Create Sample Entities
                    </button>
                </div>
            </div>

        {:else if activeTab === 'import'}
            <div class="import-tab">
                <div class="info-card">
                    <div class="info-icon">📥</div>
                    <div class="info-content">
                        <h3 class="info-title">Import Entity Definitions</h3>
                        <p class="info-description">
                            Import entity definitions from a JSON file or paste JSON data directly.
                        </p>
                    </div>
                </div>

                <div class="import-section">
                    <label for="import-data" class="field-label">JSON Data</label>
                    <textarea
                        id="import-data"
                        bind:value={importData}
                        placeholder="Paste your entity definitions JSON here..."
                        class="import-textarea"
                        rows="10"
                    ></textarea>
                    
                    <div class="import-help">
                        <p class="help-text">
                            💡 Tip: You can export entity definitions from another system and paste the JSON here.
                        </p>
                    </div>
                </div>

                <div class="actions">
                    <button 
                        class="btn btn-secondary"
                        onclick={() => importData = ''}
                        disabled={importing || !importData.trim()}
                    >
                        Clear
                    </button>
                    <button 
                        class="btn btn-primary"
                        onclick={importEntities}
                        disabled={importing || !importData.trim()}
                    >
                        {importing ? 'Importing...' : 'Import Entities'}
                    </button>
                </div>
            </div>

        {:else if activeTab === 'export'}
            <div class="export-tab">
                <div class="info-card">
                    <div class="info-icon">📤</div>
                    <div class="info-content">
                        <h3 class="info-title">Export Entity Definitions</h3>
                        <p class="info-description">
                            Export your current entity definitions as a JSON file for backup or sharing.
                        </p>
                        <div class="info-stats">
                            <div class="stat">
                                <span class="stat-value">{entityCount}</span>
                                <span class="stat-label">Entities to Export</span>
                            </div>
                        </div>
                    </div>
                </div>

                {#if entityCount === 0}
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <h4 class="empty-title">No Entities to Export</h4>
                        <p class="empty-description">
                            You don't have any entity definitions to export. Create some entities first.
                        </p>
                    </div>
                {:else}
                    <div class="export-info">
                        <p class="export-description">
                            This will download a JSON file containing all your entity definitions. 
                            You can use this file to backup your entities or import them into another system.
                        </p>
                    </div>

                    <div class="actions">
                        <button 
                            class="btn btn-primary"
                            onclick={exportEntities}
                            disabled={exporting}
                        >
                            {exporting ? 'Exporting...' : 'Export Entities'}
                        </button>
                    </div>

                    <div class="danger-zone">
                        <h4 class="danger-title">Danger Zone</h4>
                        <p class="danger-description">
                            Clear all entity definitions. This action cannot be undone.
                        </p>
                        <button 
                            class="btn btn-danger"
                            onclick={clearAllEntities}
                            disabled={importing || exporting}
                        >
                            Clear All Entities
                        </button>
                    </div>
                {/if}
            </div>
        {/if}
    </div>

    <!-- Footer Actions -->
    <div class="migration-footer">
        <button class="btn btn-secondary" onclick={onComplete}>
            Close
        </button>
    </div>
</div>

<style>
    .entity-migration {
        @apply max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6 space-y-6;
    }

    .migration-header {
        @apply text-center space-y-2;
    }

    .migration-title {
        @apply text-2xl font-bold text-gray-900;
    }

    .migration-description {
        @apply text-gray-600;
    }

    .tabs {
        @apply flex border-b border-gray-200;
    }

    .tab {
        @apply px-6 py-3 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent transition-colors duration-200;
    }

    .tab.active {
        @apply text-blue-600 border-blue-600;
    }

    .tab-content {
        @apply py-6;
    }

    .info-card {
        @apply flex items-start space-x-4 p-6 bg-gray-50 rounded-lg mb-6;
    }

    .info-icon {
        @apply text-3xl;
    }

    .info-content {
        @apply flex-1 space-y-3;
    }

    .info-title {
        @apply text-lg font-semibold text-gray-900;
    }

    .info-description {
        @apply text-gray-600;
    }

    .info-stats {
        @apply flex space-x-6;
    }

    .stat {
        @apply text-center;
    }

    .stat-value {
        @apply block text-2xl font-bold text-gray-900;
    }

    .stat-label {
        @apply text-sm text-gray-500;
    }

    .section-title {
        @apply text-lg font-semibold text-gray-900 mb-4;
    }

    .entity-list {
        @apply space-y-4;
    }

    .entity-preview {
        @apply flex items-start space-x-3 p-4 border border-gray-200 rounded-lg;
    }

    .entity-icon {
        @apply text-2xl;
    }

    .entity-info {
        @apply flex-1 space-y-1;
    }

    .entity-name {
        @apply font-semibold text-gray-900;
    }

    .entity-desc {
        @apply text-sm text-gray-600;
    }

    .entity-fields {
        @apply text-xs text-gray-500;
    }

    .field-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .import-textarea {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm;
    }

    .import-help {
        @apply mt-3;
    }

    .help-text {
        @apply text-sm text-gray-600;
    }

    .export-info {
        @apply mb-6;
    }

    .export-description {
        @apply text-gray-600;
    }

    .warning-message {
        @apply flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800 mb-4;
    }

    .warning-icon {
        @apply text-lg;
    }

    .empty-state {
        @apply text-center py-8 space-y-4;
    }

    .empty-icon {
        @apply text-6xl;
    }

    .empty-title {
        @apply text-xl font-semibold text-gray-900;
    }

    .empty-description {
        @apply text-gray-600;
    }

    .danger-zone {
        @apply mt-8 pt-6 border-t border-red-200 space-y-3;
    }

    .danger-title {
        @apply text-lg font-semibold text-red-900;
    }

    .danger-description {
        @apply text-red-600;
    }

    .actions {
        @apply flex justify-end space-x-3;
    }

    .migration-footer {
        @apply flex justify-end pt-6 border-t border-gray-200;
    }

    .btn {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
    }

    .btn:disabled {
        @apply opacity-50 cursor-not-allowed;
    }

    .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700;
    }

    .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400;
    }

    .btn-danger {
        @apply bg-red-600 text-white hover:bg-red-700;
    }
</style>
