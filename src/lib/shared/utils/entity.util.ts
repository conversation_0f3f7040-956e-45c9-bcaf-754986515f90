import { z } from "zod";
import type { 
    EntityField, 
    ValidationRule, 
    EntityDefinition, 
    EntityInstance,
    ConditionalLogic 
} from "../../types/entity.type";

// Generate Zod schema from entity definition
export function generateEntitySchema(definition: EntityDefinition): z.ZodObject<any> {
    const schemaFields: Record<string, z.ZodTypeAny> = {};

    definition.fields.forEach(field => {
        let fieldSchema = createFieldSchema(field);
        
        // Apply validation rules
        field.validation.forEach(rule => {
            fieldSchema = applyValidationRule(fieldSchema, rule);
        });

        // Handle optional fields
        if (!field.required) {
            fieldSchema = fieldSchema.optional();
        }

        schemaFields[field.name] = fieldSchema;
    });

    return z.object(schemaFields);
}

// Create base field schema based on field type
function createFieldSchema(field: EntityField): z.ZodTypeAny {
    switch (field.type) {
        case 'text':
        case 'textarea':
        case 'password':
        case 'phone':
        case 'color':
            return z.string();
        
        case 'email':
            return z.string().email();
        
        case 'url':
            return z.string().url();
        
        case 'number':
        case 'range':
            let numberSchema = z.number();
            if (field.min !== undefined) numberSchema = numberSchema.min(field.min);
            if (field.max !== undefined) numberSchema = numberSchema.max(field.max);
            return numberSchema;
        
        case 'date':
        case 'datetime':
        case 'time':
            return z.string().refine(val => !isNaN(Date.parse(val)), {
                message: "Invalid date format"
            });
        
        case 'boolean':
            return z.boolean();
        
        case 'select':
        case 'radio':
            if (field.options && field.options.length > 0) {
                const values = field.options.map(opt => opt.value);
                return z.enum(values as [string, ...string[]]);
            }
            return z.string();
        
        case 'multiselect':
        case 'checkbox':
            if (field.options && field.options.length > 0) {
                const values = field.options.map(opt => opt.value);
                return z.array(z.enum(values as [string, ...string[]]));
            }
            return z.array(z.string());
        
        case 'file':
            return field.multiple ? z.array(z.string()) : z.string();
        
        case 'location':
            return z.object({
                latitude: z.number(),
                longitude: z.number(),
                address: z.string().optional()
            });
        
        default:
            return z.string();
    }
}

// Apply validation rule to schema
function applyValidationRule(schema: z.ZodTypeAny, rule: ValidationRule): z.ZodTypeAny {
    switch (rule.type) {
        case 'required':
            // Required is handled at field level
            return schema;
        
        case 'minLength':
            if (schema instanceof z.ZodString) {
                return schema.min(rule.value, rule.message);
            }
            return schema;
        
        case 'maxLength':
            if (schema instanceof z.ZodString) {
                return schema.max(rule.value, rule.message);
            }
            return schema;
        
        case 'min':
            if (schema instanceof z.ZodNumber) {
                return schema.min(rule.value, rule.message);
            }
            return schema;
        
        case 'max':
            if (schema instanceof z.ZodNumber) {
                return schema.max(rule.value, rule.message);
            }
            return schema;
        
        case 'pattern':
            if (schema instanceof z.ZodString) {
                return schema.regex(new RegExp(rule.value), rule.message);
            }
            return schema;
        
        case 'email':
            if (schema instanceof z.ZodString) {
                return schema.email(rule.message);
            }
            return schema;
        
        case 'url':
            if (schema instanceof z.ZodString) {
                return schema.url(rule.message);
            }
            return schema;
        
        case 'custom':
            // Custom validation would need to be implemented based on rule.value
            return schema;
        
        default:
            return schema;
    }
}

// Validate entity instance data
export function validateEntityData(definition: EntityDefinition, data: Record<string, any>) {
    const schema = generateEntitySchema(definition);
    return schema.safeParse(data);
}

// Check if field should be visible based on conditional logic
export function isFieldVisible(field: EntityField, formData: Record<string, any>): boolean {
    if (!field.conditional) return true;

    const { field: watchField, operator, value } = field.conditional;
    const watchValue = formData[watchField];

    switch (operator) {
        case 'equals':
            return watchValue === value;
        case 'notEquals':
            return watchValue !== value;
        case 'contains':
            return String(watchValue).includes(String(value));
        case 'notContains':
            return !String(watchValue).includes(String(value));
        case 'greaterThan':
            return Number(watchValue) > Number(value);
        case 'lessThan':
            return Number(watchValue) < Number(value);
        case 'isEmpty':
            return !watchValue || watchValue === '' || (Array.isArray(watchValue) && watchValue.length === 0);
        case 'isNotEmpty':
            return watchValue && watchValue !== '' && (!Array.isArray(watchValue) || watchValue.length > 0);
        default:
            return true;
    }
}

// Get default value for a field
export function getFieldDefaultValue(field: EntityField): any {
    if (field.defaultValue !== undefined) {
        return field.defaultValue;
    }

    switch (field.type) {
        case 'text':
        case 'textarea':
        case 'email':
        case 'password':
        case 'phone':
        case 'url':
        case 'color':
            return '';
        case 'number':
        case 'range':
            return field.min || 0;
        case 'boolean':
            return false;
        case 'date':
        case 'datetime':
        case 'time':
            return '';
        case 'select':
        case 'radio':
            return field.options?.[0]?.value || '';
        case 'multiselect':
        case 'checkbox':
            return [];
        case 'file':
            return field.multiple ? [] : '';
        case 'location':
            return { latitude: 0, longitude: 0, address: '' };
        default:
            return '';
    }
}

// Initialize form data with default values
export function initializeFormData(definition: EntityDefinition): Record<string, any> {
    const formData: Record<string, any> = {};
    
    definition.fields.forEach(field => {
        formData[field.name] = getFieldDefaultValue(field);
    });

    return formData;
}

// Format field value for display
export function formatFieldValue(field: EntityField, value: any): string {
    if (value == null) return '';

    switch (field.type) {
        case 'boolean':
            return value ? 'Yes' : 'No';
        case 'date':
            return new Date(value).toLocaleDateString();
        case 'datetime':
            return new Date(value).toLocaleString();
        case 'time':
            return new Date(`1970-01-01T${value}`).toLocaleTimeString();
        case 'multiselect':
        case 'checkbox':
            if (Array.isArray(value)) {
                return value.join(', ');
            }
            return String(value);
        case 'location':
            if (typeof value === 'object' && value.address) {
                return value.address;
            }
            return `${value.latitude}, ${value.longitude}`;
        case 'file':
            if (Array.isArray(value)) {
                return `${value.length} file(s)`;
            }
            return value ? '1 file' : 'No file';
        default:
            return String(value);
    }
}

// Sort entity fields by order
export function sortFieldsByOrder(fields: EntityField[]): EntityField[] {
    return [...fields].sort((a, b) => a.order - b.order);
}

// Filter visible fields based on conditional logic
export function getVisibleFields(fields: EntityField[], formData: Record<string, any>): EntityField[] {
    return fields.filter(field => isFieldVisible(field, formData));
}
