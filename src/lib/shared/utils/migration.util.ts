import type { EntityDefinition, EntityField } from "../../types/entity.type";
import { entityDefinitionService } from "../../home/<USER>/entity.service";

// Sample entity definitions that mirror existing fleet entities
export const sampleEntityDefinitions: Omit<EntityDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>[] = [
    {
        name: 'customer',
        displayName: 'Customer',
        description: 'Customer information and contact details',
        icon: '👤',
        color: '#3B82F6',
        fields: [
            {
                id: 'field_name',
                name: 'name',
                label: 'Full Name',
                type: 'text',
                required: true,
                placeholder: 'Enter customer name',
                validation: [
                    { type: 'required', message: 'Name is required', value: undefined }
                ],
                order: 0
            },
            {
                id: 'field_email',
                name: 'email',
                label: 'Email Address',
                type: 'email',
                required: true,
                placeholder: '<EMAIL>',
                validation: [
                    { type: 'required', message: '<PERSON>ail is required', value: undefined },
                    { type: 'email', message: 'Must be a valid email', value: undefined }
                ],
                order: 1
            },
            {
                id: 'field_phone',
                name: 'phone',
                label: 'Phone Number',
                type: 'phone',
                required: false,
                placeholder: '+****************',
                validation: [],
                order: 2
            },
            {
                id: 'field_company',
                name: 'company',
                label: 'Company',
                type: 'text',
                required: false,
                placeholder: 'Company name',
                validation: [],
                order: 3
            },
            {
                id: 'field_address',
                name: 'address',
                label: 'Address',
                type: 'textarea',
                required: false,
                placeholder: 'Full address',
                rows: 3,
                validation: [],
                order: 4
            },
            {
                id: 'field_status',
                name: 'status',
                label: 'Status',
                type: 'select',
                required: true,
                defaultValue: 'active',
                options: [
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' },
                    { value: 'prospect', label: 'Prospect' }
                ],
                validation: [
                    { type: 'required', message: 'Status is required', value: undefined }
                ],
                order: 5
            }
        ],
        timestamps: true,
        softDelete: true,
        searchableFields: ['name', 'email', 'company'],
        displayField: 'name'
    },
    {
        name: 'product',
        displayName: 'Product',
        description: 'Product catalog and inventory management',
        icon: '📦',
        color: '#10B981',
        fields: [
            {
                id: 'field_name',
                name: 'name',
                label: 'Product Name',
                type: 'text',
                required: true,
                placeholder: 'Enter product name',
                validation: [
                    { type: 'required', message: 'Product name is required', value: undefined }
                ],
                order: 0
            },
            {
                id: 'field_sku',
                name: 'sku',
                label: 'SKU',
                type: 'text',
                required: true,
                placeholder: 'Product SKU',
                validation: [
                    { type: 'required', message: 'SKU is required', value: undefined }
                ],
                order: 1
            },
            {
                id: 'field_description',
                name: 'description',
                label: 'Description',
                type: 'textarea',
                required: false,
                placeholder: 'Product description',
                rows: 4,
                validation: [],
                order: 2
            },
            {
                id: 'field_price',
                name: 'price',
                label: 'Price',
                type: 'number',
                required: true,
                min: 0,
                step: 0.01,
                placeholder: '0.00',
                validation: [
                    { type: 'required', message: 'Price is required', value: undefined },
                    { type: 'min', message: 'Price must be positive', value: 0 }
                ],
                order: 3
            },
            {
                id: 'field_category',
                name: 'category',
                label: 'Category',
                type: 'select',
                required: true,
                options: [
                    { value: 'electronics', label: 'Electronics' },
                    { value: 'automotive', label: 'Automotive' },
                    { value: 'tools', label: 'Tools' },
                    { value: 'parts', label: 'Parts' },
                    { value: 'accessories', label: 'Accessories' }
                ],
                validation: [
                    { type: 'required', message: 'Category is required', value: undefined }
                ],
                order: 4
            },
            {
                id: 'field_stock',
                name: 'stock',
                label: 'Stock Quantity',
                type: 'number',
                required: true,
                min: 0,
                defaultValue: 0,
                validation: [
                    { type: 'required', message: 'Stock quantity is required', value: undefined },
                    { type: 'min', message: 'Stock cannot be negative', value: 0 }
                ],
                order: 5
            },
            {
                id: 'field_active',
                name: 'active',
                label: 'Active',
                type: 'boolean',
                required: false,
                defaultValue: true,
                validation: [],
                order: 6
            }
        ],
        timestamps: true,
        softDelete: true,
        searchableFields: ['name', 'sku', 'description'],
        displayField: 'name'
    },
    {
        name: 'service_request',
        displayName: 'Service Request',
        description: 'Customer service requests and support tickets',
        icon: '🔧',
        color: '#F59E0B',
        fields: [
            {
                id: 'field_title',
                name: 'title',
                label: 'Request Title',
                type: 'text',
                required: true,
                placeholder: 'Brief description of the request',
                validation: [
                    { type: 'required', message: 'Title is required', value: undefined }
                ],
                order: 0
            },
            {
                id: 'field_description',
                name: 'description',
                label: 'Description',
                type: 'textarea',
                required: true,
                placeholder: 'Detailed description of the service request',
                rows: 5,
                validation: [
                    { type: 'required', message: 'Description is required', value: undefined }
                ],
                order: 1
            },
            {
                id: 'field_priority',
                name: 'priority',
                label: 'Priority',
                type: 'select',
                required: true,
                defaultValue: 'medium',
                options: [
                    { value: 'low', label: 'Low' },
                    { value: 'medium', label: 'Medium' },
                    { value: 'high', label: 'High' },
                    { value: 'urgent', label: 'Urgent' }
                ],
                validation: [
                    { type: 'required', message: 'Priority is required', value: undefined }
                ],
                order: 2
            },
            {
                id: 'field_status',
                name: 'status',
                label: 'Status',
                type: 'select',
                required: true,
                defaultValue: 'open',
                options: [
                    { value: 'open', label: 'Open' },
                    { value: 'in_progress', label: 'In Progress' },
                    { value: 'pending', label: 'Pending' },
                    { value: 'resolved', label: 'Resolved' },
                    { value: 'closed', label: 'Closed' }
                ],
                validation: [
                    { type: 'required', message: 'Status is required', value: undefined }
                ],
                order: 3
            },
            {
                id: 'field_customer_email',
                name: 'customer_email',
                label: 'Customer Email',
                type: 'email',
                required: true,
                placeholder: '<EMAIL>',
                validation: [
                    { type: 'required', message: 'Customer email is required', value: undefined },
                    { type: 'email', message: 'Must be a valid email', value: undefined }
                ],
                order: 4
            },
            {
                id: 'field_due_date',
                name: 'due_date',
                label: 'Due Date',
                type: 'date',
                required: false,
                validation: [],
                order: 5
            },
            {
                id: 'field_estimated_hours',
                name: 'estimated_hours',
                label: 'Estimated Hours',
                type: 'number',
                required: false,
                min: 0,
                step: 0.5,
                validation: [
                    { type: 'min', message: 'Hours must be positive', value: 0 }
                ],
                order: 6
            }
        ],
        timestamps: true,
        softDelete: true,
        searchableFields: ['title', 'description', 'customer_email'],
        displayField: 'title'
    }
];

// Migration utilities
export const migrationUtils = {
    // Create sample entities
    createSampleEntities(): void {
        sampleEntityDefinitions.forEach(definition => {
            try {
                entityDefinitionService.create(definition);
                console.log(`Created sample entity: ${definition.displayName}`);
            } catch (error) {
                console.error(`Failed to create entity ${definition.displayName}:`, error);
            }
        });
    },

    // Check if sample entities exist
    sampleEntitiesExist(): boolean {
        const existingEntities = entityDefinitionService.getAll();
        return sampleEntityDefinitions.some(sample => 
            existingEntities.some(existing => existing.name === sample.name)
        );
    },

    // Migrate existing fleet data to dynamic entities (placeholder)
    migrateFleetData(): void {
        // This would be implemented to migrate existing Vehicle, Driver, etc. data
        // to the new dynamic entity system
        console.log('Fleet data migration would be implemented here');
        
        // Example migration logic:
        // 1. Create entity definitions for Vehicle, Driver, Battery, etc.
        // 2. Transform existing data to match entity instance format
        // 3. Import data into entity instance store
        // 4. Maintain backward compatibility
    },

    // Create entity definition from existing type structure
    createEntityFromType(
        name: string,
        displayName: string,
        typeStructure: Record<string, any>,
        options: {
            description?: string;
            icon?: string;
            color?: string;
            searchableFields?: string[];
            displayField?: string;
        } = {}
    ): Omit<EntityDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> {
        const fields: EntityField[] = Object.entries(typeStructure).map(([key, value], index) => {
            let fieldType: EntityField['type'] = 'text';
            let validation: EntityField['validation'] = [];
            let options: EntityField['options'] = undefined;

            // Infer field type from value
            if (typeof value === 'number') {
                fieldType = 'number';
            } else if (typeof value === 'boolean') {
                fieldType = 'boolean';
            } else if (key.toLowerCase().includes('email')) {
                fieldType = 'email';
                validation.push({ type: 'email', message: 'Must be a valid email', value: undefined });
            } else if (key.toLowerCase().includes('phone')) {
                fieldType = 'phone';
            } else if (key.toLowerCase().includes('date')) {
                fieldType = 'date';
            } else if (key.toLowerCase().includes('url')) {
                fieldType = 'url';
                validation.push({ type: 'url', message: 'Must be a valid URL', value: undefined });
            } else if (Array.isArray(value)) {
                fieldType = 'multiselect';
                // Could infer options from array values
            }

            return {
                id: `field_${key}`,
                name: key,
                label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
                type: fieldType,
                required: false, // Default to optional, can be overridden
                validation,
                options,
                order: index
            };
        });

        return {
            name,
            displayName,
            description: options.description || `Dynamic entity for ${displayName}`,
            icon: options.icon || '📋',
            color: options.color || '#6B7280',
            fields,
            timestamps: true,
            softDelete: true,
            searchableFields: options.searchableFields || fields.slice(0, 3).map(f => f.name),
            displayField: options.displayField || fields[0]?.name || 'id'
        };
    },

    // Export entity definitions for backup
    exportEntityDefinitions(): string {
        const entities = entityDefinitionService.getAll();
        return JSON.stringify(entities, null, 2);
    },

    // Import entity definitions from backup
    importEntityDefinitions(jsonData: string): { success: boolean; imported: number; errors: string[] } {
        try {
            const entities = JSON.parse(jsonData) as EntityDefinition[];
            let imported = 0;
            const errors: string[] = [];

            entities.forEach(entity => {
                try {
                    // Remove ID and timestamps to create as new
                    const { id, createdAt, updatedAt, createdBy, ...entityData } = entity;
                    entityDefinitionService.create(entityData);
                    imported++;
                } catch (error) {
                    errors.push(`Failed to import ${entity.displayName}: ${error}`);
                }
            });

            return { success: true, imported, errors };
        } catch (error) {
            return { success: false, imported: 0, errors: [`Invalid JSON data: ${error}`] };
        }
    },

    // Clear all entity definitions (use with caution)
    clearAllEntities(): void {
        const entities = entityDefinitionService.getAll();
        entities.forEach(entity => {
            entityDefinitionService.delete(entity.id);
        });
        console.log(`Cleared ${entities.length} entity definitions`);
    }
};

// Auto-initialize sample entities if none exist
export const initializeSampleEntities = () => {
    if (!migrationUtils.sampleEntitiesExist()) {
        migrationUtils.createSampleEntities();
        console.log('Sample entities initialized');
    }
};
