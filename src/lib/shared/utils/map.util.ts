import { Loader } from "@googlemaps/js-api-loader";

export const initMap = async (mapElement: HTMLElement, initialCenter?: { latitude: number; longitude: number; }) => {
    const loader = new Loader({
        apiKey: "AIzaSyAJ17kW-KH0sEhOx5fWl0WJBJBr3toUb4Q",
        version: "weekly",
    });
    const center: google.maps.LatLng | google.maps.LatLngLiteral = initialCenter !== undefined ? {
        lat: initialCenter!.latitude,
        lng: initialCenter!.longitude
    } : {
        lat: 48.1019851,
        lng: 11.5169496,
    };

    const { Map } = await loader.importLibrary("maps");

    const map = new Map(mapElement, {
        center: center,
        zoom: 10,
        scaleControl: true,
        rotateControl: false,
        streetViewControl: false,
        fullscreenControl: false,
    });

    return {
        map, center
    }
}
