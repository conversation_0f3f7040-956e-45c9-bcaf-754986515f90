import axios from "axios";
import { showErrorToast } from "./toast.util";
import { replace } from "svelte-spa-router";

export const get = <T>(path: string, onSuccess: (response: T) => unknown, onComplete?: () => unknown) => {
    axios.get(path, {
        baseURL: import.meta.env.VITE_SERVER_URL,
        headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`
        }
    })
        .then(response => onSuccess(response.data))
        .catch(error => handleError(error))
        .finally(onComplete);
}

export const post = <T, R>(path: string, request: T, onSuccess: (response: R) => unknown, onComplete?: () => unknown, requireAuth: boolean = true) => {
    axios.post(path, request, {
        baseURL: import.meta.env.VITE_SERVER_URL,
        headers: requireAuth ? {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`
        } : undefined
    })
        .then(response => onSuccess(response.data))
        .catch(error => handleError(error))
        .finally(onComplete);
}

export const put = <T, R>(
    path: string,
    request: T,
    onSuccess: (response: R) => unknown,
    onComplete?: () => unknown,
    config?: { headers?: Record<string, string> }
) => {
    axios.put(path, request, {
        baseURL: import.meta.env.VITE_SERVER_URL,
        headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            ...config?.headers
        }
    })
        .then(response => onSuccess(response.data))
        .catch(error => handleError(error))
        .finally(onComplete);
};

export const deleteCall = <T>(path: string, onSuccess: (response: T) => unknown, onComplete?: () => unknown) => {
    axios.delete(path, {
        baseURL: import.meta.env.VITE_SERVER_URL,
        headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        }
    })
        .then(response => onSuccess(response.data))
        .catch(error => handleError(error))
        .finally(onComplete);
}

const handleError = (error: any) => {
    if (error.code === "ERR_NETWORK") {
        showErrorToast("Server Down");
    } else {
        if (error.response.status === 401) {
            showErrorToast(error.response.data.message);
            localStorage.clear();
            replace("/");
        } else {
            showErrorToast(error.response.data.message);
        }
    }
}