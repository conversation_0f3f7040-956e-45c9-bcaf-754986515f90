import type { z } from "zod";
import type { ValidationResult } from "../../types/validation.type";

export const validateSchema = (schema: z.ZodObject<any> | z.ZodEffects<z.ZodObject<any>>, input: any) => {
    let result: ValidationResult = {
        success: false,
        errors: {}
    };
    const validationResult = schema.safeParse(input);
    result.success = validationResult.success;
    if (validationResult.error) {
        result.errors = Object.fromEntries(
            validationResult.error.errors.map(error => [error.path[0], error.message])
        );
    }
    return result;
}