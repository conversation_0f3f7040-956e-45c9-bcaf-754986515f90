<script lang="ts">
    import { push } from "svelte-spa-router";
    import { toast } from "svelte-sonner";
    import { userStore } from "../store/user.svelte";
    import Loader from "../shared/components/Loader.svelte";
    import { post } from "../shared/utils/network.util";
    import type { AuthResponse } from "../types/user.type";
    import Input from "../shared/components/Input.svelte";

    let email = $state("");
    let password = $state("");
    let requesting = $state(false);
    const login = (event: Event) => {
        requesting = true;
        event.preventDefault();
        event.stopPropagation();
        post<
            {
                email: string;
                password: string;
            },
            AuthResponse
        >(
            "/auth/login",
            {
                email: email,
                password: password,
            },
            (response) => {
                localStorage.setItem(
                    "access_token",
                    response.token.accessToken,
                );
                localStorage.setItem("user", JSON.stringify(response.user));
                userStore.user = response.user;
                push("/home");
                toast.success("Welcome admin");
            },
            () => {
                requesting = false;
            },
            false,
        );
    };
</script>

<div
    class="w-screen h-screen overflow-hidden flex justify-center items-center"
    style="background-image: url(/images/bg.svg); background-repeat: no-repeat; background-size: cover;"
>
    <div
        class="w-1/2 h-auto min-h-fit flex flex-col items-center bg-secondary border border-secondary rounded-lg shadow-2xl px-2 py-2"
    >
        <img src="/images/fleet-logo.png" alt="" />
        <form onsubmit={login} class="w-1/2 h-full flex flex-col gap-4 mt-10">
            <Input placeholder="Email" type="email" bind:value={email} />
            <Input
                placeholder="Password"
                type="password"
                bind:value={password}
            />
            <button
                class="flex justify-center items-center mt-5 rounded-md bg-primary py-2 px-4 border border-transparent text-center text-sm text-white transition-all shadow-md hover:shadow-lg focus:shadow-none hover:cursor-pointer"
            >
                {#if requesting}
                    <Loader />
                {:else}
                    Login
                {/if}
            </button>
            <div class="w-full mt-10 flex flex-col gap-10 items-center">
                <span class="text-white">You don't have account?</span>
                <a
                    href="/#/register"
                    class="flex justify-center items-center w-full rounded-md bg-tertiery py-2 px-4 border border-transparent text-center text-sm text-white transition-all shadow-md hover:shadow-lg focus:shadow-none hover:cursor-pointer"
                >
                    Register
                </a>
            </div>
        </form>
    </div>
</div>
