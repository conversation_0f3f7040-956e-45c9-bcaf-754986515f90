<script lang="ts">
    import { replace } from "svelte-spa-router";
    import Loader from "../shared/components/Loader.svelte";
    import { post } from "../shared/utils/network.util";
    import Input from "../shared/components/Input.svelte";
    import { z } from "zod";
    import { validateSchema } from "../shared/utils/validation.util";
    import type { ValidationResult } from "../types/validation.type";
    import type { Response } from "../types/response.type";
    import { showSuccessToast } from "../shared/utils/toast.util";
    import type { RegisterRequest } from "../types/request.type";

    let formState: Partial<RegisterRequest> = $state({});
    let requesting = $state(false);
    let currentForm: "account" | "company" = $state("account");
    let accountValidationResult = $state<ValidationResult>();
    let companyValidationResult = $state<ValidationResult>();
    let accountSchema = z
        .object({
            email: z
                .string({ required_error: "Must provide a valid email" })
                .trim()
                .email({ message: "Must provide a valid email" }),
            name: z.string({ required_error: "Must provide a name" }).trim(),
            password: z
                .string({ required_error: "Must provide a password" })
                .trim()
                .min(8, { message: "Password must be at least 8 characters" }),
            confirmPassword: z
                .string({ required_error: "Must provide confirm password" })
                .trim(),
        })
        .superRefine(({ confirmPassword, password }, ctx) => {
            if (confirmPassword !== password) {
                ctx.addIssue({
                    code: "custom",
                    message: "Password and confirm password don't match",
                    path: ["confirmPassword"],
                });
            }
        });
    let companySchema = z.object({
        company: z
            .string({ required_error: "Must provide company name" })
            .trim()
            .min(1, { message: "Must provide company name" }),
        address: z
            .string({ required_error: "Must provide company address" })
            .trim()
            .min(1, { message: "Must provide company address" }),
        phoneNumber: z
            .string({ required_error: "Must provide phone number" })
            .trim()
            .min(1, { message: "Must provide phone number" }),
    });

    const validateAccountInfo = () => {
        accountValidationResult = validateSchema(accountSchema, {
            email: formState.email,
            name: formState.name,
            password: formState.password,
            confirmPassword: formState.confirmPassword,
        });
        if (accountValidationResult.success) {
            currentForm = "company";
        }
    };

    const register = () => {
        companyValidationResult = validateSchema(companySchema, {
            company: formState.company,
            address: formState.address,
            phoneNumber: formState.phoneNumber,
            profilePhoto: formState.profilePhoto,
        });

        if (companyValidationResult.success) {
            requesting = true;
            const request = new FormData();
            request.append("email", formState.email!);
            request.append("name", formState.name!);
            request.append("password", formState.password!);
            request.append("confirmPassword", formState.confirmPassword!);
            request.append("company", formState.company!);
            request.append("address", formState.address!);
            request.append("phoneNumber", formState.phoneNumber!);
            request.append("profilePhoto", formState.profilePhoto!);
            post<FormData, Response>(
                "/auth/register",
                request,
                (_) => {
                    showSuccessToast(
                        "Please check your email form confirmation",
                    );
                    replace("/");
                },
                () => {
                    requesting = false;
                },
                false,
            );
        }
    };
</script>

<div
    class="w-screen h-screen overflow-hidden flex justify-center items-center"
    style="background-image: url(/images/bg.svg); background-repeat: no-repeat; background-size: cover;"
>
    <div
        class="w-1/2 h-1/2 min-h-fit flex flex-col items-center bg-secondary border border-secondary rounded-lg shadow-2xl px-2 py-2 overflow-hidden"
    >
        <img src="/images/fleet-logo.png" alt="" />
        <form
            onsubmit={register}
            class="w-1/2 h-full flex flex-col gap-4 mt-10 overflow-y-auto"
        >
            {#if currentForm === "account"}
                {@render accountForm()}
            {:else}
                {@render companyForm()}
            {/if}
            {#if currentForm === "company"}
                <button
                    class="flex justify-center items-center mt-5 rounded-md bg-primary py-2 px-4 border border-transparent text-center text-sm text-white transition-all shadow-md hover:shadow-lg focus:shadow-none hover:cursor-pointer"
                >
                    {#if requesting}
                        <Loader />
                    {:else}
                        Register
                    {/if}
                </button>
                <button
                    class="flex justify-center items-center mt-5 rounded-md bg-tertiery py-2 px-4 border border-transparent text-center text-sm text-white transition-all shadow-md hover:shadow-lg focus:shadow-none hover:cursor-pointer"
                    onclick={() => (currentForm = "account")}
                >
                    Back
                </button>
            {/if}
        </form>
    </div>
</div>

{#snippet accountForm()}
    <Input
        placeholder="Email"
        type="email"
        bind:value={formState.email}
        errorMessage={accountValidationResult?.errors["email"]}
    />
    <Input
        placeholder="Name"
        type="text"
        bind:value={formState.name}
        errorMessage={accountValidationResult?.errors["name"]}
    />
    <Input
        placeholder="Password"
        type="password"
        bind:value={formState.password}
        errorMessage={accountValidationResult?.errors["password"]}
    />
    <Input
        placeholder="Confirm Password"
        type="password"
        bind:value={formState.confirmPassword}
        errorMessage={accountValidationResult?.errors["confirmPassword"]}
    />
    <button
        class="flex justify-center items-center mt-5 rounded-md bg-primary py-2 px-4 border border-transparent text-center text-sm text-white transition-all shadow-md hover:shadow-lg focus:shadow-none hover:cursor-pointer"
        onclick={validateAccountInfo}
    >
        Next
    </button>
{/snippet}
{#snippet companyForm()}
    <Input
        placeholder="Company"
        type="text"
        bind:value={formState.company}
        errorMessage={companyValidationResult?.errors["company"]}
    />
    <Input
        placeholder="Address"
        bind:value={formState.address}
        errorMessage={companyValidationResult?.errors["address"]}
    />
    <Input
        placeholder="Phone Number"
        bind:value={formState.phoneNumber}
        errorMessage={companyValidationResult?.errors["phoneNumber"]}
    />
    <Input
        placeholder=""
        type="file"
        onChange={(event) => {
            formState.profilePhoto = event.target?.files[0];
        }}
        errorMessage={companyValidationResult?.errors["profilePhoto"]}
    />
{/snippet}
