# Dynamic Entity System Documentation

## Overview

The Dynamic Entity System allows users to create custom entities and forms without writing code. This system transforms your application from having fixed schemas to supporting user-defined data structures, similar to a form builder or CMS.

## Key Features

- **23 Field Types**: Text, number, email, date, select, file upload, location, relationships, calculated fields, and more
- **Dynamic Forms**: Automatically generated forms based on entity definitions
- **Data Management**: Full CRUD operations with search, filtering, and pagination
- **Validation**: Comprehensive validation rules with custom error messages
- **Relationships**: Link entities together with various relationship types
- **Calculated Fields**: Auto-computed fields based on formulas
- **Import/Export**: Backup and restore entity definitions
- **Sample Templates**: Pre-built entity templates to get started quickly

## Architecture

### Core Components

1. **Entity Definitions** (`EntityDefinition`): Schema that defines the structure of custom entities
2. **Entity Fields** (`EntityField`): Individual field configurations with types, validation, and behavior
3. **Entity Instances** (`EntityInstance`): Actual data records created from entity definitions
4. **Dynamic Forms** (`DynamicForm`): Auto-generated forms for data entry and editing
5. **Entity Management UI**: Interface for creating and managing entity definitions

### Data Flow

```
Entity Definition → Dynamic Form → Entity Instance → Data Storage
```

## Field Types

### Basic Fields
- **text**: Single-line text input
- **textarea**: Multi-line text input
- **number**: Numeric input with min/max constraints
- **email**: Email address with validation
- **password**: Password input (masked)
- **phone**: Phone number input
- **url**: URL input with validation

### Date & Time
- **date**: Date picker
- **datetime**: Date and time picker
- **time**: Time picker

### Selection Fields
- **select**: Dropdown selection (single choice)
- **multiselect**: Multiple selection dropdown
- **radio**: Radio button group
- **checkbox**: Checkbox group
- **boolean**: Yes/No toggle

### Advanced Fields
- **file**: File upload with type restrictions
- **color**: Color picker
- **range**: Slider input
- **location**: GPS coordinates (latitude/longitude)
- **relationship**: Link to other entities
- **calculated**: Auto-computed based on formula

## Getting Started

### 1. Access Entity Management

Navigate to the "Entities" section in the main menu to access the entity management interface.

### 2. Create Your First Entity

1. Click "Create Entity" button
2. Fill in basic information:
   - **Name**: Internal identifier (e.g., "customer")
   - **Display Name**: User-friendly name (e.g., "Customer")
   - **Description**: Optional description
   - **Icon**: Emoji or icon for visual identification

### 3. Add Fields

For each field you want to add:

1. Click "Add Field"
2. Configure field properties:
   - **Label**: Display name for the field
   - **Type**: Choose from 23 available field types
   - **Required**: Whether the field is mandatory
   - **Validation**: Add validation rules as needed
   - **Help Text**: Optional guidance for users

### 4. Configure Entity Settings

- **Searchable Fields**: Select which fields can be searched
- **Display Field**: Choose the primary field for list views
- **Timestamps**: Enable created/updated timestamps
- **Soft Delete**: Enable soft deletion (recommended)

### 5. Save and Use

Once saved, your entity will be available for:
- Creating data records
- Viewing and managing data
- Exporting data
- Building relationships with other entities

## Example: Customer Entity

Here's a complete example of a Customer entity definition:

```typescript
{
  name: "customer",
  displayName: "Customer",
  description: "Customer information and contact details",
  icon: "👤",
  fields: [
    {
      name: "name",
      label: "Full Name",
      type: "text",
      required: true,
      validation: [
        { type: "required", message: "Name is required" }
      ]
    },
    {
      name: "email",
      label: "Email Address", 
      type: "email",
      required: true,
      validation: [
        { type: "required", message: "Email is required" },
        { type: "email", message: "Must be a valid email" }
      ]
    },
    {
      name: "phone",
      label: "Phone Number",
      type: "phone",
      required: false
    },
    {
      name: "company",
      label: "Company",
      type: "text",
      required: false
    },
    {
      name: "status",
      label: "Status",
      type: "select",
      required: true,
      options: [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
        { value: "prospect", label: "Prospect" }
      ]
    }
  ],
  searchableFields: ["name", "email", "company"],
  displayField: "name"
}
```

## Validation Rules

### Available Validation Types

- **required**: Field must have a value
- **minLength**: Minimum text length
- **maxLength**: Maximum text length
- **min**: Minimum numeric value
- **max**: Maximum numeric value
- **email**: Valid email format
- **url**: Valid URL format
- **pattern**: Custom regex pattern

### Example Validation Rules

```typescript
validation: [
  { type: "required", message: "This field is required" },
  { type: "minLength", value: 3, message: "Must be at least 3 characters" },
  { type: "maxLength", value: 50, message: "Must be less than 50 characters" },
  { type: "pattern", value: "^[A-Z]", message: "Must start with uppercase letter" }
]
```

## Relationships

### Relationship Types

- **one-to-one**: Each record links to exactly one record in another entity
- **one-to-many**: One record can link to multiple records in another entity
- **many-to-one**: Multiple records can link to one record in another entity
- **many-to-many**: Records can link to multiple records in both directions

### Example: Order → Customer Relationship

```typescript
{
  name: "customer_id",
  label: "Customer",
  type: "relationship",
  relationshipType: "many-to-one",
  relatedEntity: "customer",
  relatedField: "name",
  required: true
}
```

## Calculated Fields

Calculated fields automatically compute values based on other fields using JavaScript expressions.

### Example: Total Price Calculation

```typescript
{
  name: "total",
  label: "Total Price",
  type: "calculated",
  calculationFormula: "quantity * price * (1 + tax_rate)",
  calculationDependencies: ["quantity", "price", "tax_rate"],
  recalculateOnChange: true
}
```

## Data Management

### Creating Records

1. Navigate to your entity's records page
2. Click "Add Record"
3. Fill in the dynamic form
4. Click "Create Record"

### Viewing and Editing

- **List View**: See all records in a table format
- **Search**: Find records using searchable fields
- **Filter**: Apply filters to narrow down results
- **Sort**: Sort by any field
- **Pagination**: Navigate through large datasets

### Export Options

- **CSV Export**: Download data as comma-separated values
- **JSON Export**: Download data in JSON format

## Sample Entities

The system includes three pre-built sample entities:

### 1. Customer Entity
- Name, email, phone, company, address, status
- Perfect for CRM applications

### 2. Product Entity  
- Name, SKU, description, price, category, stock
- Ideal for inventory management

### 3. Service Request Entity
- Title, description, priority, status, customer email, due date
- Great for support ticket systems

## Migration and Import/Export

### Creating Sample Entities

Use the "Manage Entities" button to:
- Create sample entities
- Import entity definitions from JSON
- Export current entities for backup
- Clear all entities (use with caution)

### Importing Entities

1. Click "Manage Entities" → "Import" tab
2. Paste JSON data or upload a file
3. Click "Import Entities"

### Exporting Entities

1. Click "Manage Entities" → "Export" tab  
2. Click "Export Entities"
3. A JSON file will be downloaded

## Best Practices

### Entity Design

1. **Keep it Simple**: Start with essential fields, add more later
2. **Use Clear Names**: Choose descriptive field labels
3. **Set Validation**: Add appropriate validation rules
4. **Plan Relationships**: Think about how entities connect
5. **Test Early**: Create sample records to test your design

### Field Configuration

1. **Required Fields**: Only mark truly essential fields as required
2. **Help Text**: Provide guidance for complex fields
3. **Default Values**: Set sensible defaults where appropriate
4. **Field Order**: Arrange fields in logical order
5. **Validation Messages**: Write clear, helpful error messages

### Performance

1. **Searchable Fields**: Limit to 3-5 most important fields
2. **Display Field**: Choose a unique, descriptive field
3. **Pagination**: Use appropriate page sizes (10-50 records)
4. **Indexing**: Consider which fields need fast lookup

## Troubleshooting

### Common Issues

**Entity not appearing**: Check that the entity was saved successfully and refresh the page.

**Validation errors**: Review field validation rules and ensure they match your data requirements.

**Relationship not working**: Verify that the related entity exists and the field names are correct.

**Calculated field not updating**: Check the formula syntax and ensure all dependent fields exist.

### Getting Help

1. Check the browser console for error messages
2. Verify entity definitions are complete
3. Test with simple data first
4. Use sample entities as reference

## API Reference

### Entity Definition Service

```typescript
// Create entity
entityDefinitionService.create(definition)

// Get all entities  
entityDefinitionService.getAll()

// Get entity by ID
entityDefinitionService.getById(id)

// Update entity
entityDefinitionService.update(id, definition)

// Delete entity
entityDefinitionService.delete(id)
```

### Entity Instance Service

```typescript
// Create instance
entityInstanceService.create(entityId, data)

// Get all instances
entityInstanceService.getAll(entityId)

// Update instance
entityInstanceService.update(entityId, instanceId, data)

// Delete instance
entityInstanceService.delete(entityId, instanceId)

// Search instances
entityInstanceService.search(entityId, query)
```

This documentation provides a comprehensive guide to using the Dynamic Entity System. For additional help or advanced use cases, refer to the code examples in the sample entities or contact your system administrator.
