{"name": "fleet", "private": true, "version": "0.8.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.app.json && tsc -p tsconfig.node.json"}, "devDependencies": {"@flaticon/flaticon-uicons": "^3.3.1", "@googlemaps/js-api-loader": "^1.16.8", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/vite": "^4.0.8", "@tsconfig/svelte": "^5.0.4", "@types/google.maps": "^3.58.1", "axios": "^1.7.9", "lightweight-charts": "^5.0.5", "socket.io-client": "^4.8.1", "svelte": "^5.19.6", "svelte-check": "^4.1.4", "svelte-gauge": "^2.0.0", "svelte-lightweight-charts": "^2.2.0", "svelte-sonner": "^0.3.28", "svelte-spa-router": "^4.0.1", "tailwindcss": "^4.0.8", "typescript": "~5.7.2", "vite": "^6.1.0", "zod": "^3.24.3"}}