# Dynamic Entity System

A powerful, flexible system that transforms your application from fixed schemas to user-defined data structures. Create custom entities, forms, and data management interfaces without writing code.

## 🚀 Features

- **23 Field Types**: From basic text to advanced relationships and calculated fields
- **Dynamic Forms**: Auto-generated forms based on entity definitions
- **Full CRUD**: Complete data management with search, filter, sort, and pagination
- **Validation Engine**: Comprehensive validation with custom rules and messages
- **Relationships**: Link entities with one-to-one, one-to-many, many-to-many relationships
- **Calculated Fields**: Auto-computed values using JavaScript expressions
- **File Uploads**: Enhanced file handling with drag-drop interface
- **Import/Export**: Backup and restore entity definitions
- **Sample Templates**: Pre-built entities to get started quickly
- **Migration Tools**: Seamlessly integrate with existing data

## 📋 Quick Start

### 1. Try Sample Entities
```bash
# Navigate to Entities → Manage Entities → Create Sample Entities
```

### 2. Create Your First Entity
```typescript
// Example: Simple Task entity
{
  name: "task",
  displayName: "Task",
  fields: [
    { name: "title", type: "text", required: true },
    { name: "description", type: "textarea" },
    { name: "priority", type: "select", options: [...] },
    { name: "due_date", type: "date" },
    { name: "completed", type: "boolean" }
  ]
}
```

### 3. Start Managing Data
- Navigate to your entity's records page
- Click "Add Record" to create entries
- Use search, filters, and sorting to manage data
- Export data as CSV or JSON

## 🏗️ Architecture

### Core Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Entity          │    │ Dynamic          │    │ Entity          │
│ Definition      │───▶│ Form             │───▶│ Instance        │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Field           │    │ Dynamic          │    │ Data            │
│ Configuration   │    │ Field            │    │ Storage         │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### File Structure

```
src/lib/
├── types/
│   └── entity.type.ts          # Core type definitions
├── store/
│   └── entity.store.svelte.ts  # State management
├── home/
│   ├── service/
│   │   └── entity.service.ts   # Business logic
│   └── ui/
│       ├── EntityManagement.svelte    # Entity CRUD
│       └── EntityInstances.svelte     # Data management
└── shared/
    ├── components/
    │   ├── DynamicForm.svelte         # Form generator
    │   ├── DynamicField.svelte        # Field renderer
    │   ├── EntityBuilder.svelte       # Entity designer
    │   ├── FieldBuilder.svelte        # Field designer
    │   └── EntityMigration.svelte     # Import/export
    └── utils/
        ├── entity.util.ts             # Helper functions
        └── migration.util.ts          # Migration tools
```

## 🔧 Field Types

### Basic Fields
- **text**: Single-line text input
- **textarea**: Multi-line text input  
- **number**: Numeric input with constraints
- **email**: Email with validation
- **password**: Masked password input
- **phone**: Phone number input
- **url**: URL with validation

### Date & Time
- **date**: Date picker
- **datetime**: Date and time picker
- **time**: Time picker

### Selection
- **select**: Single choice dropdown
- **multiselect**: Multiple choice dropdown
- **radio**: Radio button group
- **checkbox**: Checkbox group
- **boolean**: Yes/No toggle

### Advanced
- **file**: File upload with restrictions
- **color**: Color picker
- **range**: Slider input
- **location**: GPS coordinates
- **relationship**: Entity relationships
- **calculated**: Computed fields

## 🔗 Relationships

### Supported Types
- **one-to-one**: User ↔ Profile
- **one-to-many**: Customer → Orders
- **many-to-one**: Orders → Customer  
- **many-to-many**: Users ↔ Roles

### Example Configuration
```typescript
{
  name: "customer_id",
  type: "relationship",
  relationshipType: "many-to-one",
  relatedEntity: "customer",
  relatedField: "name"
}
```

## 🧮 Calculated Fields

Auto-compute values using JavaScript expressions:

```typescript
{
  name: "total_price",
  type: "calculated",
  calculationFormula: "quantity * unit_price * (1 + tax_rate)",
  calculationDependencies: ["quantity", "unit_price", "tax_rate"],
  recalculateOnChange: true
}
```

## ✅ Validation System

### Built-in Rules
- **required**: Field must have value
- **minLength/maxLength**: Text length constraints
- **min/max**: Numeric constraints
- **email**: Valid email format
- **url**: Valid URL format
- **pattern**: Custom regex validation

### Example Usage
```typescript
validation: [
  { type: "required", message: "This field is required" },
  { type: "minLength", value: 3, message: "At least 3 characters" },
  { type: "email", message: "Must be valid email" }
]
```

## 📊 Data Management

### Features
- **Search**: Full-text search across searchable fields
- **Filter**: Advanced filtering with operators
- **Sort**: Multi-column sorting
- **Pagination**: Efficient large dataset handling
- **Export**: CSV and JSON export options
- **Bulk Operations**: Mass data operations

### API Examples
```typescript
// Create entity instance
const result = entityInstanceService.create('customer', {
  name: 'John Doe',
  email: '<EMAIL>',
  status: 'active'
});

// Search instances
const results = entityInstanceService.search('customer', 'john');

// Filter with operators
const filtered = entityInstanceService.filter('customer', {
  status: { operator: 'equals', value: 'active' },
  created_at: { operator: 'greaterThan', value: '2024-01-01' }
});
```

## 🔄 Migration & Import/Export

### Sample Entities
Pre-built templates for common use cases:
- **Customer**: CRM and contact management
- **Product**: Inventory and catalog management  
- **Service Request**: Support ticket system

### Import/Export
```typescript
// Export all entities
const backup = migrationUtils.exportEntityDefinitions();

// Import from backup
const result = migrationUtils.importEntityDefinitions(jsonData);
```

## 🎨 UI Components

### EntityManagement
Main interface for managing entity definitions:
- Create, edit, delete entities
- View entity statistics
- Access migration tools

### EntityInstances  
Data management interface for entity records:
- CRUD operations on data
- Search and filtering
- Pagination and sorting
- Export functionality

### DynamicForm
Auto-generated forms based on entity definitions:
- All field types supported
- Real-time validation
- Conditional field display
- Responsive design

## 🚀 Getting Started

### 1. Installation
The system is already integrated into your application. Access it via the "Entities" menu.

### 2. Quick Setup
```bash
# Try sample entities first
Navigate to: Entities → Manage Entities → Create Sample Entities

# Create your first entity
Navigate to: Entities → Create Entity
```

### 3. Documentation
- **Full Guide**: `docs/DYNAMIC_ENTITIES.md`
- **Quick Start**: `docs/QUICK_START.md`
- **API Reference**: See service files for detailed API

## 🛠️ Development

### Key Technologies
- **Svelte 5**: Modern reactive framework
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Zod**: Runtime validation
- **Local Storage**: Data persistence

### Architecture Patterns
- **Component-based**: Modular, reusable components
- **Service Layer**: Business logic separation
- **Store Pattern**: Centralized state management
- **Type-driven**: Comprehensive TypeScript types

## 📈 Performance

### Optimizations
- **Lazy Loading**: Components loaded on demand
- **Pagination**: Efficient large dataset handling
- **Caching**: Smart caching of entity definitions
- **Validation**: Client-side validation for speed

### Best Practices
- Limit searchable fields to 3-5 most important
- Use appropriate page sizes (10-50 records)
- Set sensible validation rules
- Plan entity relationships carefully

## 🤝 Contributing

### Code Style
- Follow existing TypeScript patterns
- Use Svelte 5 syntax (`$state`, `$derived`, etc.)
- Apply Tailwind CSS classes consistently
- Write comprehensive type definitions

### Testing
- Test entity creation and editing
- Verify form generation and validation
- Check data CRUD operations
- Validate import/export functionality

## 📄 License

This dynamic entity system is part of your application. Refer to your main project license.

## 🆘 Support

### Troubleshooting
1. Check browser console for errors
2. Verify entity definitions are complete
3. Test with sample entities first
4. Review validation rules and requirements

### Common Issues
- **Entity not appearing**: Refresh page, check save status
- **Validation errors**: Review field requirements and rules
- **Relationship issues**: Verify related entity exists
- **Calculated fields**: Check formula syntax and dependencies

### Getting Help
- Review documentation in `docs/` folder
- Check sample entity configurations
- Use browser developer tools for debugging
- Start with simple use cases and add complexity gradually

---

**Transform your application with user-defined data structures. Start building custom entities today!**
